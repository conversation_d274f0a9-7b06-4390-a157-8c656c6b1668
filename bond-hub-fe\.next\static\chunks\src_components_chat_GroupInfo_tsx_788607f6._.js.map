{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/GroupInfo.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Button } from \"@/components/ui/button\";\nimport { Group, User, UserInfo, Media, GroupRole } from \"@/types/base\";\nimport { getLinkIcon, getLinkTitle } from \"@/utils/link-utils\";\nimport MediaViewer from \"@/components/media/MediaViewer\";\nimport GroupInfoSocketHandler from \"../group/GroupInfoSocketHandler\";\nimport {\n  X,\n  Users,\n  UserPlus,\n  Settings,\n  LogOut,\n  Bell,\n  Pin,\n  FileImage,\n  ChevronRight,\n  Trash,\n  Video,\n  ChevronDown,\n  ArrowLeft,\n  MoreHorizontal,\n  UserMinus,\n  Shield,\n  Ban,\n  Link as LinkIcon,\n  Pencil,\n  RefreshCw,\n  QrCode,\n  Crown,\n} from \"lucide-react\";\nimport GroupDialog from \"../group/GroupDialog\";\nimport MediaGalleryView from \"./MediaGalleryView\";\nimport { <PERSON>roll<PERSON><PERSON> } from \"@/components/ui/scroll-area\";\nimport EditGroupNameDialog from \"../group/EditGroupNameDialog\";\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\nimport { getUserDataById, batchGetUserData } from \"@/actions/user.action\";\nimport {\n  Collapsible,\n  CollapsibleContent,\n  CollapsibleTrigger,\n} from \"@/components/ui/collapsible\";\nimport { useChatStore, type ChatState } from \"@/stores/chatStore\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\n\nimport { toast } from \"sonner\";\nimport {\n  getRelationship,\n  batchGetRelationships,\n} from \"@/actions/friend.action\";\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from \"@/components/ui/alert-dialog\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n  deleteGroup,\n  leaveGroup,\n  removeGroupMember,\n  updateMemberRole,\n  getGroupById,\n} from \"@/actions/group.action\";\nimport AddMemberDialog from \"../group/AddMemberDialog\";\nimport GroupQRCodeDialog from \"../GroupQRCodeDialog\";\n\ninterface GroupInfoProps {\n  group: Group | null;\n  onClose: () => void;\n  isOverlay?: boolean;\n}\n\nexport default function GroupInfo({\n  group: initialGroup,\n  onClose,\n  isOverlay = false,\n}: GroupInfoProps) {\n  // Lấy selectedGroup trực tiếp từ store để đảm bảo luôn có dữ liệu mới nhất\n  const selectedGroup = useChatStore((state) => state.selectedGroup);\n\n  // Sử dụng state để lưu trữ dữ liệu nhóm hiện tại\n  // Ưu tiên sử dụng selectedGroup từ store, nếu không có thì dùng initialGroup\n  const [group, setGroup] = useState<Group | null>(\n    selectedGroup || initialGroup,\n  );\n\n  // Cập nhật group state khi selectedGroup hoặc initialGroup thay đổi\n  useEffect(() => {\n    // Thêm throttle để tránh cập nhật quá thường xuyên\n    if (!window._lastGroupInfoStateUpdateTime) {\n      window._lastGroupInfoStateUpdateTime = {};\n    }\n\n    const groupId = selectedGroup?.id || initialGroup?.id;\n    if (!groupId) return;\n\n    const now = Date.now();\n    const lastUpdateTime = window._lastGroupInfoStateUpdateTime[groupId] || 0;\n    const timeSinceLastUpdate = now - lastUpdateTime;\n\n    // Nếu đã cập nhật trong vòng 1 giây, bỏ qua\n    if (timeSinceLastUpdate < 1000) {\n      console.log(\n        `[GroupInfo] Skipping state update, last update was ${timeSinceLastUpdate}ms ago`,\n      );\n      return;\n    }\n\n    // Cập nhật thời gian cập nhật cuối cùng\n    window._lastGroupInfoStateUpdateTime[groupId] = now;\n\n    // Ưu tiên sử dụng selectedGroup từ store\n    if (selectedGroup) {\n      console.log(\"[GroupInfo] Updating group from selectedGroup in store\");\n      console.log(\n        \"[GroupInfo] Members count:\",\n        selectedGroup.members?.length || 0,\n      );\n      setGroup(selectedGroup);\n    } else if (initialGroup) {\n      console.log(\"[GroupInfo] Updating group from initialGroup prop\");\n      console.log(\n        \"[GroupInfo] Members count:\",\n        initialGroup.members?.length || 0,\n      );\n      setGroup(initialGroup);\n    }\n  }, [selectedGroup, initialGroup]);\n  const [mediaFiles, setMediaFiles] = useState<(Media & { createdAt: Date })[]>(\n    [],\n  );\n  const [documents, setDocuments] = useState<(Media & { createdAt: Date })[]>(\n    [],\n  );\n  const [showMediaGallery, setShowMediaGallery] = useState(false);\n  const [showMediaViewer, setShowMediaViewer] = useState(false);\n  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);\n  const [links, setLinks] = useState<\n    { url: string; title: string; timestamp: Date }[]\n  >([]);\n  const [isLoadingMedia, setIsLoadingMedia] = useState(true);\n\n  // Reset media gallery and viewer when group changes\n  useEffect(() => {\n    setShowMediaGallery(false);\n    setShowMediaViewer(false);\n  }, [group?.id]);\n  const [showMembersList, setShowMembersList] = useState(false);\n  const [memberDetails, setMemberDetails] = useState<{\n    [key: string]: User & { userInfo: UserInfo };\n  }>({});\n  const [adderDetails, setAdderDetails] = useState<{ [key: string]: User }>({});\n  const [relationships, setRelationships] = useState<{ [key: string]: string }>(\n    {},\n  );\n  const [isSendingRequest] = useState<{ [key: string]: boolean }>({});\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [showLeaveDialog, setShowLeaveDialog] = useState(false);\n  const [showTransferLeadershipDialog, setShowTransferLeadershipDialog] =\n    useState(false);\n  const [showConfirmTransferDialog, setShowConfirmTransferDialog] =\n    useState(false);\n  const [newLeaderId, setNewLeaderId] = useState<string | null>(null);\n  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [currentUserRole, setCurrentUserRole] = useState<GroupRole | null>(\n    null,\n  );\n  const [showGroupDialog, setShowGroupDialog] = useState(false);\n  const [showKickDialog, setShowKickDialog] = useState(false);\n  const [showPromoteDialog, setShowPromoteDialog] = useState(false);\n  const [showDemoteDialog, setShowDemoteDialog] = useState(false);\n  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);\n  const [selectedMember, setSelectedMember] = useState<User | null>(null);\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\n  const [showFriendRequestForm, setShowFriendRequestForm] = useState(false);\n  const [openDropdownMemberId, setOpenDropdownMemberId] = useState<\n    string | null\n  >(null);\n  const [showEditNameDialog, setShowEditNameDialog] = useState(false);\n  const [activeGalleryTab, setActiveGalleryTab] = useState<\n    \"media\" | \"files\" | \"links\"\n  >(\"media\");\n  const [showGroupQRDialog, setShowGroupQRDialog] = useState(false);\n\n  const messages = useChatStore((state) => state.messages);\n  const currentUser = useAuthStore((state) => state.user);\n  // const groupSocket = useGroupSocket();\n  // Removed forceUpdate state as it was causing infinite loops\n\n  // Hàm cập nhật danh sách thành viên (sử dụng useCallback để tránh tạo hàm mới mỗi khi render)\n  const updateMembersList = useCallback(\n    async (forceRefresh = false) => {\n      const groupId = group?.id || selectedGroup?.id || initialGroup?.id;\n      if (!groupId) return false;\n\n      // Thêm throttle để tránh gọi API quá thường xuyên\n      if (!window._lastGroupInfoApiCallTime) {\n        window._lastGroupInfoApiCallTime = {};\n      }\n\n      const now = Date.now();\n      const lastCallTime = window._lastGroupInfoApiCallTime[groupId] || 0;\n      const timeSinceLastCall = now - lastCallTime;\n\n      // Nếu đã gọi API trong vòng 2 giây và không phải là force refresh, bỏ qua\n      if (timeSinceLastCall < 2000 && !forceRefresh) {\n        console.log(\n          `[GroupInfo] Skipping API call, last call was ${timeSinceLastCall}ms ago`,\n        );\n        return true;\n      }\n\n      // Cập nhật thời gian gọi API\n      window._lastGroupInfoApiCallTime[groupId] = now;\n\n      console.log(\n        \"[GroupInfo] Updating members list for group\",\n        groupId,\n        \"forceRefresh:\",\n        forceRefresh,\n      );\n\n      // Check if we have a valid cache for this group\n      const chatStore = useChatStore.getState() as ChatState & {\n        setShouldFetchGroupData?: (shouldFetch: boolean) => void;\n        clearGroupCache?: (groupId: string) => void;\n      };\n      const cachedData = chatStore.groupCache\n        ? chatStore.groupCache[groupId]\n        : undefined;\n      const currentTime = new Date();\n      const isCacheValid =\n        cachedData &&\n        !forceRefresh && // Always refresh if forceRefresh is true\n        currentTime.getTime() - cachedData.lastFetched.getTime() < 30 * 1000; // 30 seconds cache\n\n      if (isCacheValid) {\n        console.log(`[GroupInfo] Using cached group data for ${groupId}`);\n\n        // Update the group state with cached data\n        setGroup(cachedData.group);\n\n        // Don't update forceUpdate here to prevent infinite loops\n        return true;\n      }\n\n      try {\n        // Lấy dữ liệu nhóm mới trực tiếp từ API để đảm bảo dữ liệu mới nhất\n        console.log(\"[GroupInfo] Fetching fresh group data from API\");\n        const result = await getGroupById(groupId);\n\n        if (result.success && result.group) {\n          console.log(\n            \"[GroupInfo] Successfully fetched fresh group data from API\",\n          );\n          console.log(\n            \"[GroupInfo] Members count:\",\n            result.group.members?.length || 0,\n          );\n          console.log(\n            \"[GroupInfo] Current members count:\",\n            group?.members?.length || 0,\n          );\n\n          // Kiểm tra xem số lượng thành viên có thay đổi không\n          const membersChanged =\n            group?.members?.length !== result.group.members?.length;\n          console.log(\"[GroupInfo] Members changed:\", membersChanged);\n\n          // Cập nhật group state với dữ liệu mới từ API\n          setGroup(result.group);\n\n          // Cập nhật selectedGroup trong store\n          chatStore.setSelectedGroup(result.group);\n\n          // Update the cache\n          if (chatStore.groupCache) {\n            // Fallback for direct cache manipulation\n            chatStore.groupCache[groupId] = {\n              group: result.group,\n              lastFetched: new Date(),\n            };\n          }\n\n          // Cập nhật conversations store để đảm bảo UI được cập nhật đồng bộ\n          useConversationsStore.getState().updateConversation(groupId, {\n            group: {\n              id: result.group.id,\n              name: result.group.name,\n              avatarUrl: result.group.avatarUrl,\n              createdAt: result.group.createdAt,\n              memberUsers: result.group.memberUsers,\n            },\n          });\n\n          // Nếu số lượng thành viên thay đổi, hiển thị thông báo\n          if (membersChanged && !forceRefresh) {\n            if (\n              (group?.members?.length || 0) <\n              (result.group.members?.length || 0)\n            ) {\n              toast.info(\"Thành viên mới đã được thêm vào nhóm\");\n            } else if (\n              (group?.members?.length || 0) >\n              (result.group.members?.length || 0)\n            ) {\n              toast.info(\"Một thành viên đã bị xóa khỏi nhóm\");\n            }\n          }\n\n          return true;\n        }\n      } catch (error) {\n        console.error(\"[GroupInfo] Error fetching group data:\", error);\n      }\n\n      // Nếu không thể lấy dữ liệu từ API, thử dùng dữ liệu từ store\n      const storeSelectedGroup = chatStore.selectedGroup;\n      if (storeSelectedGroup && storeSelectedGroup.id === groupId) {\n        console.log(\"[GroupInfo] Falling back to store data\");\n        setGroup(storeSelectedGroup);\n        // Don't update forceUpdate here to prevent infinite loops\n        return true;\n      }\n\n      return false;\n    },\n    [group?.id, group?.members?.length, selectedGroup?.id, initialGroup?.id],\n  );\n\n  // Hàm làm mới dữ liệu nhóm\n  const handleRefreshGroup = async () => {\n    toast.info(\"Đang làm mới dữ liệu nhóm...\");\n\n    try {\n      // Force refresh by passing true to updateMembersList\n      const success = await updateMembersList(true);\n\n      if (success) {\n        toast.success(\"Làm mới dữ liệu nhóm thành công\");\n        return;\n      }\n\n      // Nếu không thể cập nhật qua updateMembersList, thử sử dụng refreshSelectedGroup\n      console.log(\"[GroupInfo] Trying refreshSelectedGroup as fallback\");\n\n      // Clear the cache to force a refresh\n      const groupId = group?.id || selectedGroup?.id || initialGroup?.id;\n      if (groupId) {\n        const chatStore = useChatStore.getState();\n        // Clear the cache entry for this group\n        if (chatStore.groupCache && chatStore.groupCache[groupId]) {\n          delete chatStore.groupCache[groupId];\n        }\n      }\n\n      await useChatStore.getState().refreshSelectedGroup();\n\n      // Kiểm tra xem selectedGroup đã được cập nhật chưa\n      const updatedSelectedGroup = useChatStore.getState().selectedGroup;\n\n      if (updatedSelectedGroup && updatedSelectedGroup.id === groupId) {\n        console.log(\"[GroupInfo] Successfully refreshed group data via store\");\n        console.log(\n          \"[GroupInfo] New members count:\",\n          updatedSelectedGroup.members?.length || 0,\n        );\n\n        // Cập nhật group state\n        setGroup(updatedSelectedGroup);\n\n        // No need to update forceUpdate to prevent infinite loops\n\n        // Cập nhật conversations store để đảm bảo UI được cập nhật đồng bộ\n        if (groupId && updatedSelectedGroup) {\n          useConversationsStore.getState().updateConversation(groupId, {\n            group: {\n              id: updatedSelectedGroup.id,\n              name: updatedSelectedGroup.name,\n              avatarUrl: updatedSelectedGroup.avatarUrl,\n              createdAt: updatedSelectedGroup.createdAt,\n              memberUsers: updatedSelectedGroup.memberUsers,\n            },\n          });\n        }\n\n        toast.success(\"Làm mới dữ liệu nhóm thành công\");\n        return;\n      }\n\n      // Nếu không thể lấy dữ liệu từ API, thử dùng triggerGroupsReload\n      if (typeof window !== \"undefined\" && window.triggerGroupsReload) {\n        console.log(\"[GroupInfo] Triggering global group reload event\");\n        window.triggerGroupsReload();\n        // No need to update forceUpdate to prevent infinite loops\n      } else {\n        toast.error(\"Không thể làm mới dữ liệu nhóm\");\n      }\n    } catch (error) {\n      console.error(\"Error refreshing group data:\", error);\n      toast.error(\"Không thể làm mới dữ liệu nhóm\");\n    }\n  };\n\n  // Sử dụng cách tiếp cận giống với ChatHeader để lấy thông tin nhóm từ conversationsStore\n  // Lấy danh sách cuộc trò chuyện từ conversationsStore\n  const conversations = useConversationsStore((state) => state.conversations);\n\n  // Tìm thông tin nhóm từ conversationsStore\n  const groupConversation = useMemo(() => {\n    if (!initialGroup?.id) return null;\n    return conversations.find(\n      (conv) => conv.type === \"GROUP\" && conv.group?.id === initialGroup.id,\n    );\n  }, [conversations, initialGroup?.id]);\n\n  // Tính toán số lượng thành viên từ conversationsStore\n  const memberCount = useMemo(() => {\n    // Ưu tiên sử dụng thông tin từ conversationsStore\n    if (groupConversation?.group?.memberUsers) {\n      return groupConversation.group.memberUsers.length;\n    }\n    // Nếu không có, sử dụng thông tin từ group state\n    return group?.members?.length || 0;\n  }, [groupConversation?.group?.memberUsers, group?.members]);\n\n  // Lấy thông tin chi tiết của các thành viên và vai trò của người dùng hiện tại\n  useEffect(() => {\n    if (group?.id && group.members) {\n      const fetchMemberDetails = async () => {\n        const newMemberDetails: {\n          [key: string]: User & { userInfo: UserInfo };\n        } = {};\n        const newAdderDetails: { [key: string]: User } = {};\n        const newRelationships: { [key: string]: string } = {};\n\n        try {\n          // Collect all user IDs that need to be fetched\n          const memberIds: string[] = [];\n          const adderIds: string[] = [];\n          const relationshipIds: string[] = [];\n\n          // Prepare lists of IDs to fetch\n          for (const member of group.members) {\n            // Check if we need to fetch user data\n            if (!member.user?.userInfo) {\n              memberIds.push(member.userId);\n            } else {\n              // If we already have the data, store it\n              newMemberDetails[member.userId] = member.user as User & {\n                userInfo: UserInfo;\n              };\n            }\n\n            // Check if we need to fetch adder data\n            if (\n              member.addedBy &&\n              typeof member.addedBy === \"object\" &&\n              \"id\" in member.addedBy &&\n              \"fullName\" in member.addedBy\n            ) {\n              // Create a simple User object with the addedBy information\n              const adderInfo = member.addedBy as unknown as {\n                id: string;\n                fullName: string;\n              };\n              newAdderDetails[member.userId] = {\n                id: adderInfo.id,\n                userInfo: {\n                  id: adderInfo.id,\n                  fullName: adderInfo.fullName,\n                  blockStrangers: false,\n                  createdAt: new Date(),\n                  updatedAt: new Date(),\n                  userAuth: { id: adderInfo.id } as User,\n                },\n              } as unknown as User;\n            } else if (\n              member.addedById &&\n              member.addedById !== currentUser?.id &&\n              !member.addedBy\n            ) {\n              adderIds.push(member.addedById);\n            } else if (member.addedBy && \"userInfo\" in member.addedBy) {\n              newAdderDetails[member.userId] = member.addedBy as User;\n            }\n\n            // Check if we need to fetch relationship data\n            if (member.userId !== currentUser?.id) {\n              relationshipIds.push(member.userId);\n            }\n\n            // Set current user role\n            if (currentUser && member.userId === currentUser.id) {\n              setCurrentUserRole(member.role);\n            }\n          }\n\n          // Batch fetch user data\n          if (memberIds.length > 0) {\n            console.log(`Batch fetching ${memberIds.length} member details`);\n            const userResult = await batchGetUserData(memberIds);\n            if (userResult.success && userResult.users) {\n              userResult.users.forEach((user) => {\n                newMemberDetails[user.id] = user as User & {\n                  userInfo: UserInfo;\n                };\n              });\n            }\n          }\n\n          // Batch fetch adder data\n          if (adderIds.length > 0) {\n            console.log(`Batch fetching ${adderIds.length} adder details`);\n            const adderResult = await batchGetUserData(adderIds);\n            if (adderResult.success && adderResult.users) {\n              // Match adders to members\n              for (const member of group.members) {\n                if (member.addedById) {\n                  const adder = adderResult.users.find(\n                    (u) => u.id === member.addedById,\n                  );\n                  if (adder) {\n                    newAdderDetails[member.userId] = adder;\n                  }\n                }\n              }\n            }\n          }\n\n          // Batch fetch relationship data\n          if (relationshipIds.length > 0) {\n            console.log(\n              `Batch fetching ${relationshipIds.length} relationships`,\n            );\n            const accessToken =\n              useAuthStore.getState().accessToken || undefined;\n            const relationshipResult = await batchGetRelationships(\n              relationshipIds,\n              accessToken,\n            );\n\n            if (\n              relationshipResult.success &&\n              relationshipResult.relationships\n            ) {\n              // Process relationships\n              Object.entries(relationshipResult.relationships).forEach(\n                ([userId, data]) => {\n                  // Normalize relationship status\n                  const status = data.status || \"NONE\";\n\n                  // Standardize relationship values\n                  if (status === \"ACCEPTED\" || status === \"FRIEND\") {\n                    newRelationships[userId] = \"ACCEPTED\";\n                  } else if (status === \"PENDING_SENT\") {\n                    newRelationships[userId] = \"PENDING_SENT\";\n                  } else if (status === \"PENDING_RECEIVED\") {\n                    newRelationships[userId] = \"PENDING_RECEIVED\";\n                  } else {\n                    newRelationships[userId] = status;\n                  }\n\n                  console.log(\n                    `Normalized relationship with ${userId}:`,\n                    newRelationships[userId],\n                  );\n                },\n              );\n            }\n          }\n\n          // Set default relationship status for any members without data\n          for (const member of group.members) {\n            if (\n              member.userId !== currentUser?.id &&\n              !newRelationships[member.userId]\n            ) {\n              newRelationships[member.userId] = \"NONE\";\n            }\n          }\n        } catch (error) {\n          console.error(\"Error fetching member details:\", error);\n        }\n\n        // Update state with all the data we collected\n        setMemberDetails(newMemberDetails);\n        setAdderDetails(newAdderDetails);\n        setRelationships(newRelationships);\n      };\n\n      fetchMemberDetails();\n    }\n  }, [group?.id, group?.members, currentUser]); // Removed forceUpdate from dependencies\n\n  // Force refresh memberDetails when group members change (for socket updates)\n  useEffect(() => {\n    if (group?.members) {\n      // Check if any member data is missing from memberDetails\n      const missingMembers = group.members.filter(\n        (member) => !memberDetails[member.userId]?.userInfo?.fullName,\n      );\n\n      if (missingMembers.length > 0) {\n        console.log(\n          `[GroupInfo] Detected ${missingMembers.length} members with missing data, refreshing...`,\n        );\n        // Trigger a re-fetch by updating the dependency array\n        const timer = setTimeout(() => {\n          // This will trigger the main useEffect above\n        }, 100);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [group?.members, memberDetails]);\n\n  // Lấy media từ tin nhắn\n  useEffect(() => {\n    if (group?.id) {\n      setIsLoadingMedia(true);\n\n      // Lọc media từ tin nhắn hiện có\n      const extractMediaFromMessages = () => {\n        try {\n          const imageAndVideoFiles: (Media & {\n            createdAt: Date;\n            sender?: unknown;\n            senderId?: string;\n          })[] = [];\n          const documentFiles: (Media & {\n            createdAt: Date;\n            sender?: unknown;\n            senderId?: string;\n          })[] = [];\n          const extractedLinks: {\n            url: string;\n            title: string;\n            timestamp: Date;\n          }[] = [];\n\n          if (!Array.isArray(messages)) {\n            console.warn(\"[GroupInfo] Messages is not an array:\", messages);\n            return;\n          }\n\n          messages.forEach((message) => {\n            try {\n              // Skip invalid messages\n              if (!message || typeof message !== \"object\") {\n                console.warn(\"[GroupInfo] Invalid message object:\", message);\n                return;\n              }\n\n              // Skip recalled messages\n              if (message.recalled) return;\n\n              // Skip messages without content\n              if (!message.content) {\n                console.warn(\"[GroupInfo] Message without content:\", message);\n                return;\n              }\n\n              // Process media\n              const media = message.content.media;\n              if (Array.isArray(media) && media.length > 0) {\n                media.forEach((mediaItem) => {\n                  if (!mediaItem?.metadata?.extension) {\n                    console.warn(\n                      \"[GroupInfo] Media item missing metadata or extension:\",\n                      mediaItem,\n                    );\n                    return;\n                  }\n\n                  const extension = mediaItem.metadata.extension.toLowerCase();\n                  const mediaWithDate = {\n                    ...mediaItem,\n                    createdAt: new Date(message.createdAt || Date.now()),\n                    sender: message.sender,\n                    senderId: message.senderId,\n                  };\n\n                  if (\n                    [\n                      \"jpg\",\n                      \"jpeg\",\n                      \"png\",\n                      \"gif\",\n                      \"webp\",\n                      \"mp4\",\n                      \"webm\",\n                      \"mov\",\n                    ].includes(extension)\n                  ) {\n                    imageAndVideoFiles.push(mediaWithDate);\n                  } else {\n                    documentFiles.push(mediaWithDate);\n                  }\n                });\n              }\n\n              // Process links in text\n              const text = message.content.text;\n              if (typeof text === \"string\" && text.length > 0) {\n                const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n                const matches = text.match(urlRegex);\n                if (matches) {\n                  matches.forEach((url) => {\n                    try {\n                      // Get domain for display\n                      const domain = url\n                        .replace(/^https?:\\/\\//, \"\")\n                        .split(\"/\")[0];\n                      // Use the utility function to get a better title\n                      const title = getLinkTitle(domain, url);\n                      extractedLinks.push({\n                        url,\n                        title,\n                        timestamp: new Date(message.createdAt || Date.now()),\n                      });\n                    } catch (error) {\n                      console.warn(\n                        \"[GroupInfo] Error processing URL:\",\n                        url,\n                        error,\n                      );\n                    }\n                  });\n                }\n              }\n            } catch (error) {\n              console.warn(\n                \"[GroupInfo] Error processing message:\",\n                message,\n                error,\n              );\n            }\n          });\n\n          // Sort media from newest to oldest\n          imageAndVideoFiles.sort(\n            (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\n          );\n          documentFiles.sort(\n            (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\n          );\n          extractedLinks.sort(\n            (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),\n          );\n\n          setMediaFiles(imageAndVideoFiles.slice(0, 20)); // Limit to 20 files\n          setDocuments(documentFiles.slice(0, 10)); // Limit to 10 files\n          setLinks(extractedLinks.slice(0, 10)); // Limit to 10 links\n        } catch (error) {\n          console.error(\n            \"[GroupInfo] Error in extractMediaFromMessages:\",\n            error,\n          );\n        } finally {\n          setIsLoadingMedia(false);\n        }\n      };\n\n      extractMediaFromMessages();\n    }\n  }, [group?.id, messages]);\n\n  // Kiểm tra nếu không có dữ liệu nhóm\n  useEffect(() => {\n    if (!initialGroup && !group) {\n      console.log(\"[GroupInfo] No group data available\");\n      // Đóng GroupInfo nếu không có dữ liệu nhóm\n      if (onClose) {\n        onClose();\n      }\n    }\n  }, [initialGroup, group, onClose]);\n\n  if (!group) {\n    return (\n      <div className=\"h-full flex items-center justify-center\">\n        <div className=\"text-center p-4\">\n          <div className=\"animate-spin h-8 w-8 border-2 border-blue-500 rounded-full border-t-transparent mx-auto mb-4\"></div>\n          <p>Đang tải thông tin nhóm...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const handleMemberClick = async (memberId: string) => {\n    // Kiểm tra xem đã có thông tin thành viên trong memberDetails chưa\n    if (memberDetails[memberId]) {\n      setSelectedMember(memberDetails[memberId]);\n      setShowProfileDialog(true);\n      return;\n    }\n\n    // Nếu member.user đã có đầy đủ thông tin, sử dụng luôn mà không cần gọi API\n    const memberWithData = group?.members?.find(\n      (m) => m.userId === memberId && m.user?.userInfo,\n    );\n    if (memberWithData?.user) {\n      setSelectedMember(memberWithData.user as User & { userInfo: UserInfo });\n      setShowProfileDialog(true);\n      return;\n    }\n\n    try {\n      // Nếu không có sẵn thông tin, gọi API để lấy\n      const result = await getUserDataById(memberId);\n      if (result.success && result.user) {\n        setSelectedMember(result.user as User & { userInfo: UserInfo });\n        setShowProfileDialog(true);\n      }\n    } catch (error) {\n      console.error(\"Error fetching member data:\", error);\n      // Không hiển thị dialog nếu không thể lấy thông tin chi tiết\n      console.log(\n        \"Không thể lấy thông tin thành viên, bỏ qua việc mở ProfileDialog\",\n      );\n    }\n  };\n\n  if (showMediaGallery) {\n    return (\n      <MediaGalleryView\n        mediaFiles={mediaFiles}\n        documents={documents}\n        links={links}\n        initialTab={activeGalleryTab}\n        onClose={() => setShowMediaGallery(false)}\n      />\n    );\n  }\n\n  // Handle send friend request\n  const handleSendFriendRequest = (userId: string) => {\n    const memberData = memberDetails[userId];\n    if (memberData) {\n      setSelectedMember(memberData);\n      setShowFriendRequestForm(true);\n      setShowProfileDialog(true);\n      setOpenDropdownMemberId(null); // Close dropdown after action\n    }\n  };\n\n  if (showMembersList) {\n    return (\n      <div\n        className={`h-full flex flex-col bg-white ${!isOverlay ? \"border-l\" : \"\"}`}\n      >\n        <div className=\"p-4 flex items-center justify-between border-b\">\n          <div className=\"flex items-center\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"mr-2\"\n              onClick={() => setShowMembersList(false)}\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n            </Button>\n            <h2 className=\"font-semibold\">Thành viên</h2>\n          </div>\n        </div>\n\n        <div className=\"p-4 border-b\">\n          <Button\n            className=\"w-full flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-black\"\n            onClick={() => {\n              setShowMembersList(false);\n              setShowAddMemberDialog(true);\n            }}\n          >\n            <UserPlus className=\"h-4 w-4\" />\n            <span>Thêm thành viên</span>\n          </Button>\n        </div>\n\n        <div className=\"p-4 flex justify-between items-center\">\n          <span className=\"text-sm\">Danh sách thành viên ({memberCount})</span>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleRefreshGroup}\n            title=\"Làm mới danh sách thành viên\"\n          >\n            <RefreshCw className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto\">\n          {/* Ưu tiên sử dụng memberUsers từ conversationsStore nếu có */}\n          {groupConversation?.group?.memberUsers\n            ? // Hiển thị danh sách thành viên từ conversationsStore\n              groupConversation.group.memberUsers.map((member) => {\n                // Get the best available name with fallback logic\n                const displayName =\n                  member.fullName || `Người dùng ${member.id.slice(-4)}`;\n\n                const initials =\n                  displayName &&\n                  displayName !== `Người dùng ${member.id.slice(-4)}`\n                    ? displayName.slice(0, 2).toUpperCase()\n                    : \"??\";\n\n                return (\n                  <div\n                    key={`${member.id}`}\n                    className=\"flex items-center p-4 hover:bg-gray-100 justify-between\"\n                  >\n                    <div\n                      className=\"flex items-center cursor-pointer\"\n                      onClick={() => handleMemberClick(member.id)}\n                    >\n                      <Avatar className=\"h-10 w-10 mr-3\">\n                        <AvatarImage\n                          src={member.profilePictureUrl || undefined}\n                          className=\"object-cover\"\n                        />\n                        <AvatarFallback className=\"bg-gray-200 text-gray-600\">\n                          {initials}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <p className=\"font-medium\">{displayName}</p>\n                        <p className=\"text-xs text-gray-500\">\n                          {member.role === \"LEADER\"\n                            ? \"Trưởng nhóm\"\n                            : member.role === \"CO_LEADER\"\n                              ? \"Phó nhóm\"\n                              : \"\"}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center\">\n                      {/* Show pending status */}\n                      {member.id !== currentUser?.id &&\n                        relationships[member.id] === \"PENDING_SENT\" && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"icon\"\n                            disabled\n                            title=\"Đã gửi lời mời kết bạn\"\n                          >\n                            <LinkIcon className=\"h-4 w-4 text-gray-400\" />\n                          </Button>\n                        )}\n\n                      {/* Hiển thị menu tùy chọn cho thành viên (không hiển thị cho chính mình) */}\n                      {member.id !== currentUser?.id && (\n                        <DropdownMenu\n                          open={openDropdownMemberId === member.id}\n                          onOpenChange={(open) => {\n                            if (open) {\n                              setOpenDropdownMemberId(member.id);\n                            } else if (openDropdownMemberId === member.id) {\n                              setOpenDropdownMemberId(null);\n                            }\n                          }}\n                        >\n                          <DropdownMenuTrigger asChild>\n                            <Button variant=\"ghost\" size=\"icon\">\n                              <MoreHorizontal className=\"h-5 w-5\" />\n                            </Button>\n                          </DropdownMenuTrigger>\n                          <DropdownMenuContent\n                            align=\"end\"\n                            onEscapeKeyDown={() =>\n                              setOpenDropdownMemberId(null)\n                            }\n                          >\n                            {/* Add friend option if not already friends */}\n                            {relationships[member.id] === \"NONE\" && (\n                              <DropdownMenuItem\n                                onClick={() =>\n                                  handleSendFriendRequest(member.id)\n                                }\n                                disabled={isSendingRequest[member.id]}\n                              >\n                                {isSendingRequest[member.id] ? (\n                                  <>\n                                    <div className=\"h-4 w-4 mr-2 rounded-full border-2 border-gray-600 border-t-transparent animate-spin\"></div>\n                                    Đang gửi...\n                                  </>\n                                ) : (\n                                  <>\n                                    <UserPlus className=\"h-4 w-4 mr-2 text-blue-500\" />\n                                    Kết bạn\n                                  </>\n                                )}\n                              </DropdownMenuItem>\n                            )}\n\n                            {/* Leader/Co-leader management options */}\n                            {(currentUserRole === \"LEADER\" ||\n                              (currentUserRole === \"CO_LEADER\" &&\n                                member.role === \"MEMBER\")) && (\n                              <>\n                                {currentUserRole === \"LEADER\" &&\n                                  member.role === \"MEMBER\" && (\n                                    <DropdownMenuItem\n                                      onClick={() =>\n                                        handlePromoteMember(member.id)\n                                      }\n                                    >\n                                      <Shield className=\"h-4 w-4 mr-2\" />\n                                      Thăng phó nhóm\n                                    </DropdownMenuItem>\n                                  )}\n                                {currentUserRole === \"LEADER\" &&\n                                  member.role === \"CO_LEADER\" && (\n                                    <DropdownMenuItem\n                                      onClick={() =>\n                                        handleDemoteMember(member.id)\n                                      }\n                                    >\n                                      <UserMinus className=\"h-4 w-4 mr-2\" />\n                                      Hạ xuống thành viên\n                                    </DropdownMenuItem>\n                                  )}\n                                {currentUserRole === \"LEADER\" &&\n                                  (member.role === \"MEMBER\" ||\n                                    member.role === \"CO_LEADER\") && (\n                                    <DropdownMenuItem\n                                      onClick={() =>\n                                        handleSelectNewLeader(member.id)\n                                      }\n                                      className=\"text-orange-500 focus:text-orange-500\"\n                                    >\n                                      <Crown className=\"h-4 w-4 mr-2\" />\n                                      Nhượng quyền trưởng nhóm\n                                    </DropdownMenuItem>\n                                  )}\n                                <DropdownMenuSeparator />\n                                <DropdownMenuItem\n                                  onClick={() => handleKickMember(member.id)}\n                                  className=\"text-red-500 focus:text-red-500\"\n                                >\n                                  <Ban className=\"h-4 w-4 mr-2\" />\n                                  Xóa khỏi nhóm\n                                </DropdownMenuItem>\n                              </>\n                            )}\n                          </DropdownMenuContent>\n                        </DropdownMenu>\n                      )}\n                    </div>\n                  </div>\n                );\n              })\n            : // Fallback sử dụng group.members nếu không có dữ liệu từ conversationsStore\n              group.members?.map((member) => {\n                // Key bao gồm forceUpdate để đảm bảo danh sách được cập nhật khi có thay đổi\n                const memberData = memberDetails[member.userId];\n\n                // Get the best available name with fallback logic\n                const displayName =\n                  memberData?.userInfo?.fullName ||\n                  member.user?.userInfo?.fullName ||\n                  `Người dùng ${member.userId.slice(-4)}`;\n\n                const initials =\n                  displayName &&\n                  displayName !== `Người dùng ${member.userId.slice(-4)}`\n                    ? displayName.slice(0, 2).toUpperCase()\n                    : \"??\";\n\n                return (\n                  <div\n                    key={`${member.userId}`}\n                    className=\"flex items-center p-4 hover:bg-gray-100 justify-between\"\n                  >\n                    <div\n                      className=\"flex items-center cursor-pointer\"\n                      onClick={() => handleMemberClick(member.userId)}\n                    >\n                      <Avatar className=\"h-10 w-10 mr-3\">\n                        <AvatarImage\n                          src={\n                            memberData?.userInfo?.profilePictureUrl ||\n                            member.user?.userInfo?.profilePictureUrl ||\n                            undefined\n                          }\n                          className=\"object-cover\"\n                        />\n                        <AvatarFallback className=\"bg-gray-200 text-gray-600\">\n                          {initials}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <p className=\"font-medium\">{displayName}</p>\n                        <p className=\"text-xs text-gray-500\">\n                          {member.role === \"LEADER\"\n                            ? \"Trưởng nhóm\"\n                            : member.role === \"CO_LEADER\"\n                              ? \"Phó nhóm\"\n                              : \"\"}\n                        </p>\n                        {/* Hiển thị thông tin người thêm */}\n                        {member.userId !== currentUser?.id && (\n                          <p className=\"text-xs text-gray-500\">\n                            {member.addedBy && \"fullName\" in member.addedBy\n                              ? `Thêm bởi ${(member.addedBy as unknown as { fullName: string }).fullName}`\n                              : adderDetails[member.userId]?.userInfo?.fullName\n                                ? `Thêm bởi ${adderDetails[member.userId]?.userInfo?.fullName}`\n                                : \"\"}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center\">\n                      {/* Show pending status */}\n                      {member.userId !== currentUser?.id &&\n                        relationships[member.userId] === \"PENDING_SENT\" && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"icon\"\n                            disabled\n                            title=\"Đã gửi lời mời kết bạn\"\n                          >\n                            <LinkIcon className=\"h-4 w-4 text-gray-400\" />\n                          </Button>\n                        )}\n\n                      {/* Hiển thị menu tùy chọn cho thành viên (không hiển thị cho chính mình) */}\n                      {member.userId !== currentUser?.id && (\n                        <DropdownMenu\n                          open={openDropdownMemberId === member.userId}\n                          onOpenChange={(open) => {\n                            if (open) {\n                              setOpenDropdownMemberId(member.userId);\n                            } else if (openDropdownMemberId === member.userId) {\n                              setOpenDropdownMemberId(null);\n                            }\n                          }}\n                        >\n                          <DropdownMenuTrigger asChild>\n                            <Button variant=\"ghost\" size=\"icon\">\n                              <MoreHorizontal className=\"h-5 w-5\" />\n                            </Button>\n                          </DropdownMenuTrigger>\n                          <DropdownMenuContent\n                            align=\"end\"\n                            onEscapeKeyDown={() =>\n                              setOpenDropdownMemberId(null)\n                            }\n                          >\n                            {/* Add friend option if not already friends */}\n                            {relationships[member.userId] === \"NONE\" && (\n                              <DropdownMenuItem\n                                onClick={() =>\n                                  handleSendFriendRequest(member.userId)\n                                }\n                                disabled={isSendingRequest[member.userId]}\n                              >\n                                {isSendingRequest[member.userId] ? (\n                                  <>\n                                    <div className=\"h-4 w-4 mr-2 rounded-full border-2 border-gray-600 border-t-transparent animate-spin\"></div>\n                                    Đang gửi...\n                                  </>\n                                ) : (\n                                  <>\n                                    <UserPlus className=\"h-4 w-4 mr-2 text-blue-500\" />\n                                    Kết bạn\n                                  </>\n                                )}\n                              </DropdownMenuItem>\n                            )}\n\n                            {/* Leader/Co-leader management options */}\n                            {(currentUserRole === \"LEADER\" ||\n                              (currentUserRole === \"CO_LEADER\" &&\n                                member.role === \"MEMBER\")) && (\n                              <>\n                                {currentUserRole === \"LEADER\" &&\n                                  member.role === \"MEMBER\" && (\n                                    <DropdownMenuItem\n                                      onClick={() =>\n                                        handlePromoteMember(member.userId)\n                                      }\n                                    >\n                                      <Shield className=\"h-4 w-4 mr-2\" />\n                                      Thăng phó nhóm\n                                    </DropdownMenuItem>\n                                  )}\n                                {currentUserRole === \"LEADER\" &&\n                                  member.role === \"CO_LEADER\" && (\n                                    <DropdownMenuItem\n                                      onClick={() =>\n                                        handleDemoteMember(member.userId)\n                                      }\n                                    >\n                                      <UserMinus className=\"h-4 w-4 mr-2\" />\n                                      Hạ xuống thành viên\n                                    </DropdownMenuItem>\n                                  )}\n                                {currentUserRole === \"LEADER\" &&\n                                  (member.role === \"MEMBER\" ||\n                                    member.role === \"CO_LEADER\") && (\n                                    <DropdownMenuItem\n                                      onClick={() =>\n                                        handleSelectNewLeader(member.userId)\n                                      }\n                                      className=\"text-orange-500 focus:text-orange-500\"\n                                    >\n                                      <Crown className=\"h-4 w-4 mr-2\" />\n                                      Nhượng quyền trưởng nhóm\n                                    </DropdownMenuItem>\n                                  )}\n                                <DropdownMenuSeparator />\n                                <DropdownMenuItem\n                                  onClick={() =>\n                                    handleKickMember(member.userId)\n                                  }\n                                  className=\"text-red-500 focus:text-red-500\"\n                                >\n                                  <Ban className=\"h-4 w-4 mr-2\" />\n                                  Xóa khỏi nhóm\n                                </DropdownMenuItem>\n                              </>\n                            )}\n                          </DropdownMenuContent>\n                        </DropdownMenu>\n                      )}\n                    </div>\n                  </div>\n                );\n              })}\n        </div>\n\n        {showProfileDialog && selectedMember && (\n          <ProfileDialog\n            user={selectedMember}\n            isOpen={showProfileDialog}\n            onOpenChange={(open) => {\n              setShowProfileDialog(open);\n              if (!open) {\n                setSelectedMember(null);\n                setShowFriendRequestForm(false);\n              }\n            }}\n            isOwnProfile={selectedMember.id === currentUser?.id}\n            initialShowFriendRequestForm={showFriendRequestForm}\n          />\n        )}\n\n        {/* Promote Member Confirmation Dialog */}\n        <AlertDialog\n          open={showPromoteDialog}\n          onOpenChange={setShowPromoteDialog}\n        >\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>Thăng cấp thành viên</AlertDialogTitle>\n              <AlertDialogDescription>\n                Bạn có chắc chắn muốn thăng cấp thành viên này lên phó nhóm? Họ\n                sẽ có quyền quản lý nhóm.\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n              <AlertDialogAction\n                onClick={executePromoteMember}\n                disabled={isProcessing}\n                className=\"bg-blue-500 hover:bg-blue-600\"\n              >\n                {isProcessing ? (\n                  <>\n                    <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\n                    Đang xử lý...\n                  </>\n                ) : (\n                  \"Thăng cấp\"\n                )}\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n\n        {/* Demote Member Confirmation Dialog */}\n        <AlertDialog open={showDemoteDialog} onOpenChange={setShowDemoteDialog}>\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>Hạ cấp thành viên</AlertDialogTitle>\n              <AlertDialogDescription>\n                Bạn có chắc chắn muốn hạ cấp phó nhóm này xuống thành viên\n                thường? Họ sẽ mất quyền quản lý nhóm.\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n              <AlertDialogAction\n                onClick={executeDemoteMember}\n                disabled={isProcessing}\n                className=\"bg-blue-500 hover:bg-blue-600\"\n              >\n                {isProcessing ? (\n                  <>\n                    <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\n                    Đang xử lý...\n                  </>\n                ) : (\n                  \"Hạ cấp\"\n                )}\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n\n        {/* Kick Member Confirmation Dialog */}\n        <AlertDialog open={showKickDialog} onOpenChange={setShowKickDialog}>\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>Xóa thành viên</AlertDialogTitle>\n              <AlertDialogDescription>\n                Bạn có chắc chắn muốn xóa thành viên này khỏi nhóm? Họ sẽ không\n                thể xem tin nhắn trong nhóm này nữa.\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n              <AlertDialogAction\n                onClick={executeKickMember}\n                disabled={isProcessing}\n                className=\"bg-red-500 hover:bg-red-600\"\n              >\n                {isProcessing ? (\n                  <>\n                    <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\n                    Đang xử lý...\n                  </>\n                ) : (\n                  \"Xóa thành viên\"\n                )}\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className={`h-full flex flex-col bg-white ${!isOverlay ? \"border-l\" : \"\"}`}\n    >\n      {/* Socket handler for real-time updates */}\n      {group && (\n        <GroupInfoSocketHandler\n          groupId={group.id}\n          onGroupUpdated={updateMembersList}\n        />\n      )}\n      <div className=\"p-4 flex items-center justify-between border-b\">\n        <h2 className=\"font-semibold\">Thông tin nhóm</h2>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"rounded-full\"\n            onClick={handleRefreshGroup}\n            title=\"Làm mới dữ liệu nhóm\"\n          >\n            <RefreshCw className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className={`${isOverlay ? \"bg-gray-100 hover:bg-gray-200\" : \"rounded-full\"}`}\n            onClick={onClose}\n          >\n            <X className=\"h-5 w-5\" />\n          </Button>\n        </div>\n      </div>\n\n      <ScrollArea className=\"flex-1\">\n        <div className=\"space-y-2 bg-[#ebecf0]\">\n          {/* Thông tin nhóm */}\n          <div className=\"flex flex-col items-center text-center bg-white p-2\">\n            <Avatar\n              className=\"h-20 w-20 mb-3 cursor-pointer\"\n              onClick={() => setShowGroupDialog(true)}\n            >\n              <AvatarImage\n                src={group.avatarUrl || undefined}\n                className=\"object-cover\"\n              />\n              <AvatarFallback className=\"text-xl\">\n                {group.name?.slice(0, 2).toUpperCase() || \"GR\"}\n              </AvatarFallback>\n            </Avatar>\n            <div className=\"flex items-center justify-center gap-2\">\n              <h2 className=\"text-lg font-semibold\">{group.name}</h2>\n              {currentUserRole === \"LEADER\" && (\n                <button\n                  className=\"text-gray-500 hover:text-blue-500 transition-colors\"\n                  onClick={() => setShowEditNameDialog(true)}\n                >\n                  <Pencil className=\"h-4 w-4\" />\n                </button>\n              )}\n            </div>\n\n            {/* Các chức năng chính */}\n            <div className=\"grid grid-cols-4 gap-4 w-full m-2\">\n              <div className=\"flex flex-col items-center\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\n                >\n                  <Bell className=\"h-6 w-6\" />\n                </Button>\n                <span className=\"text-xs\">Bật thông báo</span>\n              </div>\n\n              <div className=\"flex flex-col items-center\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\n                >\n                  <Pin className=\"h-6 w-6\" />\n                </Button>\n                <span className=\"text-xs\">Ghim hội thoại</span>\n              </div>\n\n              <div className=\"flex flex-col items-center\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1\"\n                  onClick={() => {\n                    setShowAddMemberDialog(true);\n                  }}\n                >\n                  <UserPlus className=\"h-6 w-6\" />\n                </Button>\n                <span className=\"text-xs\">Thêm thành viên</span>\n              </div>\n\n              <div className=\"flex flex-col items-center\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\n                >\n                  <Settings className=\"h-6 w-6\" />\n                </Button>\n                <span className=\"text-xs\">Quản lý nhóm</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Thành viên nhóm */}\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\n              <div className=\"flex items-center\">\n                <span className=\"font-semibold\">Thành viên nhóm</span>\n                <span className=\"text-xs text-gray-500 ml-2\">\n                  ({memberCount})\n                </span>\n              </div>\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\n            </CollapsibleTrigger>\n            <CollapsibleContent>\n              <div\n                className=\"p-3 flex items-center hover:bg-gray-50 cursor-pointer\"\n                onClick={() => setShowMembersList(true)}\n              >\n                <Users className=\"h-5 w-5 mr-2 text-gray-500\" />\n                <span className=\"text-sm\">{memberCount} thành viên</span>\n              </div>\n            </CollapsibleContent>\n          </Collapsible>\n\n          {/* Mã QR */}\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\n              <div className=\"flex items-center\">\n                <span className=\"font-semibold\">Mã QR</span>\n              </div>\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\n            </CollapsibleTrigger>\n            <CollapsibleContent>\n              <div\n                className=\"p-3 flex items-center hover:bg-gray-50 cursor-pointer\"\n                onClick={() => setShowGroupQRDialog(true)}\n              >\n                <QrCode className=\"h-5 w-5 mr-2 text-gray-500\" />\n                <span className=\"text-sm\">Mã QR nhóm</span>\n              </div>\n            </CollapsibleContent>\n          </Collapsible>\n\n          {/* Ảnh/Video */}\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\n              <div className=\"flex items-center\">\n                <span className=\"font-semibold\">Ảnh/Video</span>\n              </div>\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\n            </CollapsibleTrigger>\n            <CollapsibleContent>\n              {isLoadingMedia ? (\n                <div className=\"p-4 text-center\">\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\n                </div>\n              ) : mediaFiles.length > 0 ? (\n                <div className=\"px-3 pt-2 pb-1\">\n                  <div className=\"grid grid-cols-4 gap-1\">\n                    {mediaFiles.slice(0, 8).map((media, index) => (\n                      <div\n                        key={index}\n                        className=\"aspect-square relative overflow-hidden border border-gray-200 rounded-md cursor-pointer\"\n                        onClick={() => {\n                          setSelectedMediaIndex(index);\n                          setShowMediaViewer(true);\n                        }}\n                        title={media.fileName || \"Xem ảnh/video\"}\n                      >\n                        {media.metadata?.extension?.match(/mp4|webm|mov/i) ||\n                        media.type === \"VIDEO\" ? (\n                          <div className=\"w-full h-full relative\">\n                            <video\n                              className=\"w-full h-full object-cover\"\n                              src={media.url}\n                              muted\n                              preload=\"metadata\"\n                            />\n                            <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\n                              <Video className=\"h-5 w-5 text-white\" />\n                            </div>\n                          </div>\n                        ) : (\n                          <div\n                            className=\"w-full h-full bg-cover bg-center\"\n                            style={{ backgroundImage: `url(${media.url})` }}\n                          ></div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"mt-2 px-2\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\n                      onClick={() => {\n                        setActiveGalleryTab(\"media\");\n                        setShowMediaGallery(true);\n                      }}\n                    >\n                      Xem tất cả\n                    </Button>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"p-4 text-center\">\n                  <p className=\"text-sm text-gray-500\">\n                    Không có ảnh hoặc video nào\n                  </p>\n                </div>\n              )}\n            </CollapsibleContent>\n          </Collapsible>\n\n          {/* File */}\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\n              <div className=\"flex items-center\">\n                <span className=\"font-semibold\">File</span>\n              </div>\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\n            </CollapsibleTrigger>\n            <CollapsibleContent>\n              {isLoadingMedia ? (\n                <div className=\"p-4 text-center\">\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\n                </div>\n              ) : documents.length > 0 ? (\n                <div className=\"space-y-2 pb-2\">\n                  {documents.slice(0, 3).map((doc, index) => (\n                    <div\n                      key={index}\n                      className=\"flex items-center py-2 px-3 hover:bg-gray-200 cursor-pointer group\"\n                      onClick={() => window.open(doc.url, \"_blank\")}\n                      title={doc.fileName} // Add tooltip for full filename\n                    >\n                      <div className=\"bg-blue-100 p-2 rounded-md mr-2 flex-shrink-0\">\n                        <FileImage className=\"h-4 w-4 text-blue-500\" />\n                      </div>\n                      <div className=\"flex-1 min-w-0 mr-1\">\n                        <div className=\"flex items-center justify-between\">\n                          <p className=\"font-medium text-sm truncate max-w-[160px]\">\n                            {doc.fileName}\n                          </p>\n                          <p className=\"text-xs text-gray-500 flex-shrink-0 ml-1\">\n                            {doc.metadata?.sizeFormatted ||\n                              `${Math.round((doc.metadata?.size || 0) / 1024)} KB`}\n                          </p>\n                        </div>\n                      </div>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        className=\"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity\"\n                      >\n                        <ChevronRight className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  ))}\n                  {documents.length > 3 && (\n                    <div className=\"mt-2 px-2 text-center\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\n                        onClick={() => {\n                          setActiveGalleryTab(\"files\");\n                          setShowMediaGallery(true);\n                        }}\n                      >\n                        Xem tất cả\n                      </Button>\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <div className=\"p-4 text-center\">\n                  <p className=\"text-sm text-gray-500\">Không có file nào</p>\n                </div>\n              )}\n            </CollapsibleContent>\n          </Collapsible>\n\n          {/* Link */}\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\n              <div className=\"flex items-center\">\n                <span className=\"font-semibold\">Link</span>\n              </div>\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\n            </CollapsibleTrigger>\n            <CollapsibleContent>\n              {isLoadingMedia ? (\n                <div className=\"p-4 text-center\">\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\n                </div>\n              ) : links.length > 0 ? (\n                <div className=\"space-y-2 pb-2\">\n                  {links.slice(0, 3).map((link, index) => {\n                    // Extract domain from URL\n                    const domain = link.url\n                      .replace(/^https?:\\/\\//, \"\")\n                      .split(\"/\")[0];\n                    // Format date as DD/MM\n                    const date = new Date(link.timestamp);\n                    const formattedDate = `${date.getDate().toString().padStart(2, \"0\")}/${(date.getMonth() + 1).toString().padStart(2, \"0\")}`;\n\n                    return (\n                      <div\n                        key={index}\n                        className=\"flex items-center py-2 px-3 hover:bg-gray-200 cursor-pointer group\"\n                        onClick={() =>\n                          window.open(link.url, \"_blank\", \"noopener,noreferrer\")\n                        }\n                        title={link.title} // Add tooltip for full title\n                      >\n                        <div className=\"w-8 h-8 rounded-md mr-2 flex items-center justify-center overflow-hidden flex-shrink-0\">\n                          {getLinkIcon(domain)}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center justify-between\">\n                            <p className=\"font-medium text-sm truncate max-w-[160px]\">\n                              {getLinkTitle(\n                                domain,\n                                link.title.length > 30\n                                  ? link.title.substring(0, 30) + \"...\"\n                                  : link.title,\n                              )}\n                            </p>\n                            <p className=\"text-xs text-gray-500 flex-shrink-0 ml-1\">\n                              {formattedDate}\n                            </p>\n                          </div>\n                          <p className=\"text-xs text-blue-500 truncate max-w-[180px]\">\n                            {domain}\n                          </p>\n                        </div>\n                      </div>\n                    );\n                  })}\n                  {links.length > 3 && (\n                    <div className=\"mt-2 px-2 text-center\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\n                        onClick={() => {\n                          setActiveGalleryTab(\"links\");\n                          setShowMediaGallery(true);\n                        }}\n                      >\n                        Xem tất cả\n                      </Button>\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <div className=\"p-4 text-center\">\n                  <p className=\"text-sm text-gray-500\">Không có link nào</p>\n                </div>\n              )}\n            </CollapsibleContent>\n          </Collapsible>\n\n          {/* Cài đặt nhóm */}\n          <div className=\"space-y-1 bg-white p-2\">\n            <Button\n              variant=\"ghost\"\n              className=\"w-full justify-start text-red-500 pl-2 opacity-60\"\n              onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\n            >\n              <Trash className=\"h-5 w-5 mr-3\" />\n              <span>Xóa lịch sử trò chuyện</span>\n            </Button>\n\n            {/* Nút chuyển quyền trưởng nhóm chỉ hiển thị cho trưởng nhóm và khi có nhiều hơn 1 thành viên */}\n            {currentUserRole === \"LEADER\" &&\n              group.members &&\n              group.members.length > 1 && (\n                <Button\n                  variant=\"ghost\"\n                  className=\"w-full justify-start text-blue-500 pl-2\"\n                  onClick={() => setShowTransferLeadershipDialog(true)}\n                >\n                  <Shield className=\"h-5 w-5 mr-3\" />\n                  <span>Chuyển quyền trưởng nhóm</span>\n                </Button>\n              )}\n\n            {/* Nút giải tán nhóm chỉ hiển thị cho trưởng nhóm */}\n            {currentUserRole === \"LEADER\" && (\n              <Button\n                variant=\"ghost\"\n                className=\"w-full justify-start text-red-500 pl-2\"\n                onClick={() => setShowDeleteDialog(true)}\n              >\n                <Trash className=\"h-5 w-5 mr-3\" />\n                <span>Giải tán nhóm</span>\n              </Button>\n            )}\n\n            {/* Nút rời nhóm hiển thị cho tất cả thành viên, trừ khi trưởng nhóm là thành viên duy nhất */}\n            {!(currentUserRole === \"LEADER\" && group.members?.length === 1) && (\n              <Button\n                variant=\"ghost\"\n                className=\"w-full justify-start text-red-500 pl-2\"\n                onClick={() => {\n                  // Nếu là trưởng nhóm, hiển thị dialog chuyển quyền trưởng nhóm\n                  if (currentUserRole === \"LEADER\") {\n                    setShowTransferLeadershipDialog(true);\n                  } else {\n                    setShowLeaveDialog(true);\n                  }\n                }}\n              >\n                <LogOut className=\"h-5 w-5 mr-3\" />\n                <span>Rời nhóm</span>\n              </Button>\n            )}\n          </div>\n        </div>\n      </ScrollArea>\n\n      {showProfileDialog && selectedMember && (\n        <ProfileDialog\n          user={selectedMember}\n          isOpen={showProfileDialog}\n          onOpenChange={setShowProfileDialog}\n          isOwnProfile={selectedMember.id === currentUser?.id}\n        />\n      )}\n\n      {/* Alert Dialog xác nhận xóa nhóm */}\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>\n              Bạn có chắc chắn muốn xóa nhóm này?\n            </AlertDialogTitle>\n            <AlertDialogDescription>\n              Hành động này không thể hoàn tác. Tất cả tin nhắn và dữ liệu của\n              nhóm sẽ bị xóa vĩnh viễn.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={handleDeleteGroup}\n              disabled={isProcessing}\n              className=\"bg-red-500 hover:bg-red-600\"\n            >\n              {isProcessing ? \"Đang xử lý...\" : \"Xóa nhóm\"}\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n\n      {/* Alert Dialog xác nhận rời nhóm */}\n      <AlertDialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>\n              Bạn có chắc chắn muốn rời nhóm này?\n            </AlertDialogTitle>\n            <AlertDialogDescription>\n              Bạn sẽ không thể xem tin nhắn của nhóm này nữa trừ khi được mời\n              lại.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={handleLeaveGroup}\n              disabled={isProcessing}\n              className=\"bg-red-500 hover:bg-red-600\"\n            >\n              {isProcessing ? \"Đang xử lý...\" : \"Rời nhóm\"}\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n\n      {/* Dialog chuyển quyền trưởng nhóm */}\n      <AlertDialog\n        open={showTransferLeadershipDialog}\n        onOpenChange={setShowTransferLeadershipDialog}\n      >\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Chuyển quyền trưởng nhóm</AlertDialogTitle>\n            <AlertDialogDescription>\n              Người được chọn sẽ trở thành trưởng nhóm và có mọi quyền quản lý\n              nhóm. Bạn sẽ trở thành thành viên thường trong nhóm. Vui lòng chọn\n              một thành viên để trở thành trưởng nhóm mới.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <div className=\"max-h-[200px] overflow-y-auto my-4 border rounded-md\">\n            {group.members\n              ?.filter((member) => member.userId !== currentUser?.id) // Lọc ra các thành viên khác\n              .map((member) => {\n                const memberData = memberDetails[member.userId];\n                const initials = memberData?.userInfo?.fullName\n                  ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\n                  : \"??\";\n\n                return (\n                  <div\n                    key={`transfer-${member.userId}`}\n                    className=\"flex items-center p-3 hover:bg-gray-100 cursor-pointer\"\n                    onClick={() => handleSelectNewLeader(member.userId)}\n                  >\n                    <Avatar className=\"h-8 w-8 mr-3\">\n                      <AvatarImage\n                        src={\n                          memberData?.userInfo?.profilePictureUrl || undefined\n                        }\n                        className=\"object-cover\"\n                      />\n                      <AvatarFallback className=\"bg-gray-200 text-gray-600\">\n                        {initials}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div>\n                      <p className=\"font-medium\">\n                        {memberData?.userInfo?.fullName || \"Thành viên\"}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {member.role === \"CO_LEADER\"\n                          ? \"Phó nhóm\"\n                          : \"Thành viên\"}\n                      </p>\n                    </div>\n                  </div>\n                );\n              })}\n          </div>\n          <AlertDialogFooter>\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n\n      {/* Dialog xác nhận chuyển quyền trưởng nhóm */}\n      <AlertDialog\n        open={showConfirmTransferDialog}\n        onOpenChange={setShowConfirmTransferDialog}\n      >\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>\n              Xác nhận chuyển quyền trưởng nhóm\n            </AlertDialogTitle>\n            <AlertDialogDescription>\n              {newLeaderId && memberDetails[newLeaderId] ? (\n                <>\n                  Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho{\" \"}\n                  <strong>\n                    {memberDetails[newLeaderId]?.userInfo?.fullName ||\n                      \"Thành viên này\"}\n                  </strong>\n                  ?\n                  <br />\n                  Sau khi chuyển quyền, bạn sẽ trở thành thành viên thường trong\n                  nhóm.\n                </>\n              ) : (\n                \"Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho thành viên này?\"\n              )}\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel\n              disabled={isProcessing}\n              onClick={() => {\n                setShowConfirmTransferDialog(false);\n                setNewLeaderId(null);\n              }}\n            >\n              Hủy\n            </AlertDialogCancel>\n            <AlertDialogAction\n              onClick={executeTransferLeadership}\n              disabled={isProcessing}\n              className=\"bg-blue-500 hover:bg-blue-600\"\n            >\n              {isProcessing ? (\n                <>\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\n                  Đang xử lý...\n                </>\n              ) : (\n                \"Xác nhận\"\n              )}\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n      {showAddMemberDialog && group && (\n        <AddMemberDialog\n          isOpen={showAddMemberDialog}\n          onOpenChange={setShowAddMemberDialog}\n          groupId={group.id}\n        />\n      )}\n\n      {/* Group Dialog */}\n      {showGroupDialog && group && (\n        <GroupDialog\n          group={group}\n          isOpen={showGroupDialog}\n          onOpenChange={setShowGroupDialog}\n          mediaFiles={mediaFiles}\n        />\n      )}\n\n      {/* Edit Group Name Dialog */}\n      {group && (\n        <EditGroupNameDialog\n          group={group}\n          isOpen={showEditNameDialog}\n          onOpenChange={setShowEditNameDialog}\n          onBack={() => setShowEditNameDialog(false)}\n          onSuccess={(updatedGroup) => {\n            // Update the group in the store\n            const chatStore = useChatStore.getState();\n            if (chatStore.selectedGroup?.id === updatedGroup.id) {\n              chatStore.setSelectedGroup(updatedGroup);\n            }\n\n            // Refresh the page after a short delay to ensure all components are updated\n            setTimeout(() => {\n              window.location.reload();\n            }, 500);\n          }}\n        />\n      )}\n\n      {/* Group QR Code Dialog */}\n      {group && (\n        <GroupQRCodeDialog\n          isOpen={showGroupQRDialog}\n          onClose={() => setShowGroupQRDialog(false)}\n          groupId={group.id}\n          groupName={group.name}\n        />\n      )}\n\n      {/* Media Viewer */}\n      {showMediaViewer && mediaFiles.length > 0 && (\n        <MediaViewer\n          isOpen={showMediaViewer}\n          onClose={() => setShowMediaViewer(false)}\n          media={mediaFiles.map((media) => ({\n            ...media,\n            // Ensure type is set correctly for videos\n            type:\n              media.metadata?.extension?.match(/mp4|webm|mov/i) ||\n              media.type === \"VIDEO\"\n                ? \"VIDEO\"\n                : \"IMAGE\",\n          }))}\n          initialIndex={selectedMediaIndex}\n          chatName={group.name || \"Nhóm chat\"}\n        />\n      )}\n\n      {/* Promote Member Confirmation Dialog */}\n      <AlertDialog open={showPromoteDialog} onOpenChange={setShowPromoteDialog}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Thăng cấp thành viên</AlertDialogTitle>\n            <AlertDialogDescription>\n              Bạn có chắc chắn muốn thăng cấp thành viên này lên phó nhóm? Họ sẽ\n              có quyền quản lý nhóm.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={executePromoteMember}\n              disabled={isProcessing}\n              className=\"bg-blue-500 hover:bg-blue-600\"\n            >\n              {isProcessing ? (\n                <>\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\n                  Đang xử lý...\n                </>\n              ) : (\n                \"Thăng cấp\"\n              )}\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n\n      {/* Demote Member Confirmation Dialog */}\n      <AlertDialog open={showDemoteDialog} onOpenChange={setShowDemoteDialog}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Hạ cấp thành viên</AlertDialogTitle>\n            <AlertDialogDescription>\n              Bạn có chắc chắn muốn hạ cấp phó nhóm này xuống thành viên thường?\n              Họ sẽ mất quyền quản lý nhóm.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={executeDemoteMember}\n              disabled={isProcessing}\n              className=\"bg-blue-500 hover:bg-blue-600\"\n            >\n              {isProcessing ? (\n                <>\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\n                  Đang xử lý...\n                </>\n              ) : (\n                \"Hạ cấp\"\n              )}\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n\n      {/* Kick Member Confirmation Dialog */}\n      <AlertDialog open={showKickDialog} onOpenChange={setShowKickDialog}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Xóa thành viên</AlertDialogTitle>\n            <AlertDialogDescription>\n              Bạn có chắc chắn muốn xóa thành viên này khỏi nhóm? Họ sẽ không\n              thể xem tin nhắn trong nhóm này nữa.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={executeKickMember}\n              disabled={isProcessing}\n              className=\"bg-red-500 hover:bg-red-600\"\n            >\n              {isProcessing ? (\n                <>\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\n                  Đang xử lý...\n                </>\n              ) : (\n                \"Xóa thành viên\"\n              )}\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n    </div>\n  );\n\n  // Hàm xử lý thăng cấp thành viên lên phó nhóm\n  async function handlePromoteMember(memberId: string) {\n    setSelectedMemberId(memberId);\n    setShowPromoteDialog(true);\n    setOpenDropdownMemberId(null); // Close dropdown after action\n  }\n\n  // Hàm thực hiện thăng cấp thành viên\n  async function executePromoteMember() {\n    if (!group?.id || !selectedMemberId) return;\n    setIsProcessing(true);\n    try {\n      const result = await updateMemberRole(\n        group.id,\n        selectedMemberId,\n        GroupRole.CO_LEADER,\n      );\n      if (result.success) {\n        // Cập nhật UI hoặc reload dữ liệu nhóm\n        setShowPromoteDialog(false);\n\n        // Cập nhật vai trò trong state để UI hiển thị đúng\n        if (group.members) {\n          const updatedMembers = group.members.map((member) => {\n            if (member.userId === selectedMemberId) {\n              return { ...member, role: GroupRole.CO_LEADER };\n            }\n            return member;\n          });\n          group.members = updatedMembers;\n        }\n\n        // Force UI update by updating the group state\n        if (group.members) {\n          setGroup({ ...group, members: [...group.members] });\n        }\n\n        // Force refresh group data to ensure all components are updated\n        setTimeout(() => {\n          updateMembersList(true);\n        }, 500);\n\n        toast.success(\"Đã thăng cấp thành viên thành phó nhóm\");\n      } else {\n        toast.error(`Lỗi: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error promoting member:\", error);\n      toast.error(\"Đã xảy ra lỗi khi thăng cấp thành viên\");\n    } finally {\n      setIsProcessing(false);\n    }\n  }\n\n  // Hàm xử lý hạ cấp phó nhóm xuống thành viên thường\n  async function handleDemoteMember(memberId: string) {\n    setSelectedMemberId(memberId);\n    setShowDemoteDialog(true);\n    setOpenDropdownMemberId(null); // Close dropdown after action\n  }\n\n  // Hàm thực hiện hạ cấp thành viên\n  async function executeDemoteMember() {\n    if (!group?.id || !selectedMemberId) return;\n    setIsProcessing(true);\n    try {\n      const result = await updateMemberRole(\n        group.id,\n        selectedMemberId,\n        GroupRole.MEMBER,\n      );\n      if (result.success) {\n        // Cập nhật UI hoặc reload dữ liệu nhóm\n        setShowDemoteDialog(false);\n\n        // Cập nhật vai trò trong state để UI hiển thị đúng\n        if (group.members) {\n          const updatedMembers = group.members.map((member) => {\n            if (member.userId === selectedMemberId) {\n              return { ...member, role: GroupRole.MEMBER };\n            }\n            return member;\n          });\n          group.members = updatedMembers;\n        }\n\n        // Force UI update by updating the group state\n        if (group.members) {\n          setGroup({ ...group, members: [...group.members] });\n        }\n\n        // Force refresh group data to ensure all components are updated\n        setTimeout(() => {\n          updateMembersList(true);\n        }, 500);\n\n        toast.success(\"Đã hạ cấp thành viên xuống thành viên thường\");\n      } else {\n        toast.error(`Lỗi: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error demoting member:\", error);\n      toast.error(\"Đã xảy ra lỗi khi hạ cấp thành viên\");\n    } finally {\n      setIsProcessing(false);\n    }\n  }\n\n  // Hàm xử lý xóa thành viên khỏi nhóm\n  async function handleKickMember(memberId: string) {\n    setSelectedMemberId(memberId);\n    setShowKickDialog(true);\n    setOpenDropdownMemberId(null); // Close dropdown after action\n    // Keep the member list open when showing the kick dialog\n    // This ensures the alert dialog appears on top of the member list\n  }\n\n  // Hàm thực hiện xóa thành viên\n  async function executeKickMember() {\n    if (!group?.id || !selectedMemberId) return;\n    setIsProcessing(true);\n    try {\n      const result = await removeGroupMember(group.id, selectedMemberId);\n      if (result.success) {\n        // Cập nhật UI hoặc reload dữ liệu nhóm\n        setShowKickDialog(false);\n\n        // Cập nhật danh sách thành viên trong state để UI hiển thị đúng\n        if (group.members) {\n          const updatedMembers = group.members.filter(\n            (member) => member.userId !== selectedMemberId,\n          );\n          group.members = updatedMembers;\n        }\n\n        // Force UI update by updating the group state\n        if (group.members) {\n          setGroup({ ...group, members: [...group.members] });\n        }\n\n        toast.success(\"Đã xóa thành viên khỏi nhóm\");\n      } else {\n        toast.error(`Lỗi: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error removing member:\", error);\n      toast.error(\"Đã xảy ra lỗi khi xóa thành viên\");\n    } finally {\n      setIsProcessing(false);\n    }\n  }\n\n  // Hàm xử lý xóa nhóm\n  async function handleDeleteGroup() {\n    if (!group?.id) return;\n    setIsProcessing(true);\n    try {\n      const result = await deleteGroup(group.id);\n      if (result.success) {\n        // Đóng dialog và chuyển hướng về trang chat\n        setShowDeleteDialog(false);\n\n        // Đóng chat của nhóm này\n        const chatStore = useChatStore.getState();\n\n        // Xóa cache của nhóm này\n        chatStore.clearChatCache(\"GROUP\", group.id);\n\n        // Đặt selectedGroup về null để đóng chat\n        chatStore.setSelectedGroup(null);\n\n        // Xóa nhóm khỏi danh sách cuộc trò chuyện\n        const conversationsStore = useConversationsStore.getState();\n        conversationsStore.removeConversation(group.id);\n\n        // Đóng dialog thông tin nhóm\n        onClose();\n\n        // Thông báo cho người dùng\n        toast.success(\"Đã giải tán nhóm thành công\");\n      } else {\n        toast.error(`Lỗi: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error deleting group:\", error);\n      toast.error(\"Đã xảy ra lỗi khi giải tán nhóm\");\n    } finally {\n      setIsProcessing(false);\n    }\n  }\n\n  // Hàm xử lý khi chọn thành viên để chuyển quyền trưởng nhóm\n  function handleSelectNewLeader(memberId: string) {\n    setNewLeaderId(memberId);\n    setShowConfirmTransferDialog(true);\n  }\n\n  // Hàm xử lý chuyển quyền trưởng nhóm\n  async function executeTransferLeadership() {\n    if (!group?.id || !newLeaderId) return;\n    setIsProcessing(true);\n    try {\n      // Chuyển quyền trưởng nhóm cho thành viên được chọn\n      const result = await updateMemberRole(\n        group.id,\n        newLeaderId,\n        GroupRole.LEADER,\n      );\n\n      if (result.success) {\n        // Đóng các dialog\n        setShowConfirmTransferDialog(false);\n        setShowTransferLeadershipDialog(false);\n\n        // Cập nhật vai trò trong state để UI hiển thị đúng\n        if (group.members && currentUser) {\n          const updatedMembers = group.members.map((member) => {\n            if (member.userId === newLeaderId) {\n              return { ...member, role: GroupRole.LEADER };\n            }\n            if (member.userId === currentUser.id) {\n              return { ...member, role: GroupRole.MEMBER };\n            }\n            return member;\n          });\n          group.members = updatedMembers;\n\n          // Cập nhật vai trò của người dùng hiện tại\n          setCurrentUserRole(GroupRole.MEMBER);\n        }\n\n        // Force UI update by updating the group state\n        if (group.members) {\n          setGroup({ ...group, members: [...group.members] });\n        }\n\n        // Force refresh group data to ensure all components are updated\n        setTimeout(() => {\n          updateMembersList(true);\n        }, 500);\n\n        // Thông báo cho người dùng\n        toast.success(\"Đã chuyển quyền trưởng nhóm thành công\");\n\n        // Reset newLeaderId\n        setNewLeaderId(null);\n      } else {\n        toast.error(`Lỗi: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error transferring leadership:\", error);\n      toast.error(\"Đã xảy ra lỗi khi chuyển quyền trưởng nhóm\");\n    } finally {\n      setIsProcessing(false);\n    }\n  }\n\n  // Hàm xử lý rời nhóm\n  async function handleLeaveGroup() {\n    if (!group?.id) return;\n    setIsProcessing(true);\n    try {\n      const result = await leaveGroup(group.id);\n      if (result.success) {\n        // Đóng dialog xác nhận\n        setShowLeaveDialog(false);\n\n        // Đóng chat của nhóm này\n        const chatStore = useChatStore.getState();\n\n        // Xóa cache của nhóm này\n        chatStore.clearChatCache(\"GROUP\", group.id);\n\n        // Đặt selectedGroup về null để đóng chat\n        chatStore.setSelectedGroup(null);\n\n        // Xóa nhóm khỏi danh sách cuộc trò chuyện\n        const conversationsStore = useConversationsStore.getState();\n        conversationsStore.removeConversation(group.id);\n\n        // Đóng dialog thông tin nhóm\n        onClose();\n\n        // Thông báo cho người dùng\n        toast.success(\"Đã rời nhóm thành công\");\n      } else {\n        toast.error(`Lỗi: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error leaving group:\", error);\n      toast.error(\"Đã xảy ra lỗi khi rời nhóm\");\n    } finally {\n      setIsProcessing(false);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AAEA;AACA;AAIA;AAUA;AAOA;AAOA;AACA;;;AA9EA;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFe,SAAS,UAAU,EAChC,OAAO,YAAY,EACnB,OAAO,EACP,YAAY,KAAK,EACF;;IACf,2EAA2E;IAC3E,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;iDAAE,CAAC,QAAU,MAAM,aAAa;;IAEjE,iDAAiD;IACjD,6EAA6E;IAC7E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/B,iBAAiB;IAGnB,oEAAoE;IACpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,mDAAmD;YACnD,IAAI,CAAC,OAAO,6BAA6B,EAAE;gBACzC,OAAO,6BAA6B,GAAG,CAAC;YAC1C;YAEA,MAAM,UAAU,eAAe,MAAM,cAAc;YACnD,IAAI,CAAC,SAAS;YAEd,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,iBAAiB,OAAO,6BAA6B,CAAC,QAAQ,IAAI;YACxE,MAAM,sBAAsB,MAAM;YAElC,4CAA4C;YAC5C,IAAI,sBAAsB,MAAM;gBAC9B,QAAQ,GAAG,CACT,CAAC,mDAAmD,EAAE,oBAAoB,MAAM,CAAC;gBAEnF;YACF;YAEA,wCAAwC;YACxC,OAAO,6BAA6B,CAAC,QAAQ,GAAG;YAEhD,yCAAyC;YACzC,IAAI,eAAe;gBACjB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CACT,8BACA,cAAc,OAAO,EAAE,UAAU;gBAEnC,SAAS;YACX,OAAO,IAAI,cAAc;gBACvB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CACT,8BACA,aAAa,OAAO,EAAE,UAAU;gBAElC,SAAS;YACX;QACF;8BAAG;QAAC;QAAe;KAAa;IAChC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzC,EAAE;IAEJ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC,EAAE;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE/B,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,oBAAoB;YACpB,mBAAmB;QACrB;8BAAG;QAAC,OAAO;KAAG;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE9C,CAAC;IACJ,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,8BAA8B,gCAAgC,GACnE,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,2BAA2B,6BAA6B,GAC7D,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD;IAEF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE7D;IACF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAErD;IACF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;4CAAE,CAAC,QAAU,MAAM,QAAQ;;IACvD,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;+CAAE,CAAC,QAAU,MAAM,IAAI;;IACtD,wCAAwC;IACxC,6DAA6D;IAE7D,8FAA8F;IAC9F,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAClC,OAAO,eAAe,KAAK;YACzB,MAAM,UAAU,OAAO,MAAM,eAAe,MAAM,cAAc;YAChE,IAAI,CAAC,SAAS,OAAO;YAErB,kDAAkD;YAClD,IAAI,CAAC,OAAO,yBAAyB,EAAE;gBACrC,OAAO,yBAAyB,GAAG,CAAC;YACtC;YAEA,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,eAAe,OAAO,yBAAyB,CAAC,QAAQ,IAAI;YAClE,MAAM,oBAAoB,MAAM;YAEhC,0EAA0E;YAC1E,IAAI,oBAAoB,QAAQ,CAAC,cAAc;gBAC7C,QAAQ,GAAG,CACT,CAAC,6CAA6C,EAAE,kBAAkB,MAAM,CAAC;gBAE3E,OAAO;YACT;YAEA,6BAA6B;YAC7B,OAAO,yBAAyB,CAAC,QAAQ,GAAG;YAE5C,QAAQ,GAAG,CACT,+CACA,SACA,iBACA;YAGF,gDAAgD;YAChD,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;YAIvC,MAAM,aAAa,UAAU,UAAU,GACnC,UAAU,UAAU,CAAC,QAAQ,GAC7B;YACJ,MAAM,cAAc,IAAI;YACxB,MAAM,eACJ,cACA,CAAC,gBAAgB,yCAAyC;YAC1D,YAAY,OAAO,KAAK,WAAW,WAAW,CAAC,OAAO,KAAK,KAAK,MAAM,mBAAmB;YAE3F,IAAI,cAAc;gBAChB,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,SAAS;gBAEhE,0CAA0C;gBAC1C,SAAS,WAAW,KAAK;gBAEzB,0DAA0D;gBAC1D,OAAO;YACT;YAEA,IAAI;gBACF,oEAAoE;gBACpE,QAAQ,GAAG,CAAC;gBACZ,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;gBAElC,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;oBAClC,QAAQ,GAAG,CACT;oBAEF,QAAQ,GAAG,CACT,8BACA,OAAO,KAAK,CAAC,OAAO,EAAE,UAAU;oBAElC,QAAQ,GAAG,CACT,sCACA,OAAO,SAAS,UAAU;oBAG5B,qDAAqD;oBACrD,MAAM,iBACJ,OAAO,SAAS,WAAW,OAAO,KAAK,CAAC,OAAO,EAAE;oBACnD,QAAQ,GAAG,CAAC,gCAAgC;oBAE5C,8CAA8C;oBAC9C,SAAS,OAAO,KAAK;oBAErB,qCAAqC;oBACrC,UAAU,gBAAgB,CAAC,OAAO,KAAK;oBAEvC,mBAAmB;oBACnB,IAAI,UAAU,UAAU,EAAE;wBACxB,yCAAyC;wBACzC,UAAU,UAAU,CAAC,QAAQ,GAAG;4BAC9B,OAAO,OAAO,KAAK;4BACnB,aAAa,IAAI;wBACnB;oBACF;oBAEA,mEAAmE;oBACnE,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,SAAS;wBAC3D,OAAO;4BACL,IAAI,OAAO,KAAK,CAAC,EAAE;4BACnB,MAAM,OAAO,KAAK,CAAC,IAAI;4BACvB,WAAW,OAAO,KAAK,CAAC,SAAS;4BACjC,WAAW,OAAO,KAAK,CAAC,SAAS;4BACjC,aAAa,OAAO,KAAK,CAAC,WAAW;wBACvC;oBACF;oBAEA,uDAAuD;oBACvD,IAAI,kBAAkB,CAAC,cAAc;wBACnC,IACE,CAAC,OAAO,SAAS,UAAU,CAAC,IAC5B,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,GAClC;4BACA,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;wBACb,OAAO,IACL,CAAC,OAAO,SAAS,UAAU,CAAC,IAC5B,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,GAClC;4BACA,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;wBACb;oBACF;oBAEA,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;YAEA,8DAA8D;YAC9D,MAAM,qBAAqB,UAAU,aAAa;YAClD,IAAI,sBAAsB,mBAAmB,EAAE,KAAK,SAAS;gBAC3D,QAAQ,GAAG,CAAC;gBACZ,SAAS;gBACT,0DAA0D;gBAC1D,OAAO;YACT;YAEA,OAAO;QACT;mDACA;QAAC,OAAO;QAAI,OAAO,SAAS;QAAQ,eAAe;QAAI,cAAc;KAAG;IAG1E,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAEX,IAAI;YACF,qDAAqD;YACrD,MAAM,UAAU,MAAM,kBAAkB;YAExC,IAAI,SAAS;gBACX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;YAEA,iFAAiF;YACjF,QAAQ,GAAG,CAAC;YAEZ,qCAAqC;YACrC,MAAM,UAAU,OAAO,MAAM,eAAe,MAAM,cAAc;YAChE,IAAI,SAAS;gBACX,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,uCAAuC;gBACvC,IAAI,UAAU,UAAU,IAAI,UAAU,UAAU,CAAC,QAAQ,EAAE;oBACzD,OAAO,UAAU,UAAU,CAAC,QAAQ;gBACtC;YACF;YAEA,MAAM,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,oBAAoB;YAElD,mDAAmD;YACnD,MAAM,uBAAuB,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;YAElE,IAAI,wBAAwB,qBAAqB,EAAE,KAAK,SAAS;gBAC/D,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CACT,kCACA,qBAAqB,OAAO,EAAE,UAAU;gBAG1C,uBAAuB;gBACvB,SAAS;gBAET,0DAA0D;gBAE1D,mEAAmE;gBACnE,IAAI,WAAW,sBAAsB;oBACnC,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,SAAS;wBAC3D,OAAO;4BACL,IAAI,qBAAqB,EAAE;4BAC3B,MAAM,qBAAqB,IAAI;4BAC/B,WAAW,qBAAqB,SAAS;4BACzC,WAAW,qBAAqB,SAAS;4BACzC,aAAa,qBAAqB,WAAW;wBAC/C;oBACF;gBACF;gBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;YAEA,iEAAiE;YACjE,IAAI,aAAkB,eAAe,OAAO,mBAAmB,EAAE;gBAC/D,QAAQ,GAAG,CAAC;gBACZ,OAAO,mBAAmB;YAC1B,0DAA0D;YAC5D,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,yFAAyF;IACzF,sDAAsD;IACtD,MAAM,gBAAgB,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;0DAAE,CAAC,QAAU,MAAM,aAAa;;IAE1E,2CAA2C;IAC3C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE;YAChC,IAAI,CAAC,cAAc,IAAI,OAAO;YAC9B,OAAO,cAAc,IAAI;wDACvB,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO,aAAa,EAAE;;QAEzE;+CAAG;QAAC;QAAe,cAAc;KAAG;IAEpC,sDAAsD;IACtD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YAC1B,kDAAkD;YAClD,IAAI,mBAAmB,OAAO,aAAa;gBACzC,OAAO,kBAAkB,KAAK,CAAC,WAAW,CAAC,MAAM;YACnD;YACA,iDAAiD;YACjD,OAAO,OAAO,SAAS,UAAU;QACnC;yCAAG;QAAC,mBAAmB,OAAO;QAAa,OAAO;KAAQ;IAE1D,+EAA+E;IAC/E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO,MAAM,MAAM,OAAO,EAAE;gBAC9B,MAAM;8DAAqB;wBACzB,MAAM,mBAEF,CAAC;wBACL,MAAM,kBAA2C,CAAC;wBAClD,MAAM,mBAA8C,CAAC;wBAErD,IAAI;4BACF,+CAA+C;4BAC/C,MAAM,YAAsB,EAAE;4BAC9B,MAAM,WAAqB,EAAE;4BAC7B,MAAM,kBAA4B,EAAE;4BAEpC,gCAAgC;4BAChC,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;gCAClC,sCAAsC;gCACtC,IAAI,CAAC,OAAO,IAAI,EAAE,UAAU;oCAC1B,UAAU,IAAI,CAAC,OAAO,MAAM;gCAC9B,OAAO;oCACL,wCAAwC;oCACxC,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI;gCAG/C;gCAEA,uCAAuC;gCACvC,IACE,OAAO,OAAO,IACd,OAAO,OAAO,OAAO,KAAK,YAC1B,QAAQ,OAAO,OAAO,IACtB,cAAc,OAAO,OAAO,EAC5B;oCACA,2DAA2D;oCAC3D,MAAM,YAAY,OAAO,OAAO;oCAIhC,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;wCAC/B,IAAI,UAAU,EAAE;wCAChB,UAAU;4CACR,IAAI,UAAU,EAAE;4CAChB,UAAU,UAAU,QAAQ;4CAC5B,gBAAgB;4CAChB,WAAW,IAAI;4CACf,WAAW,IAAI;4CACf,UAAU;gDAAE,IAAI,UAAU,EAAE;4CAAC;wCAC/B;oCACF;gCACF,OAAO,IACL,OAAO,SAAS,IAChB,OAAO,SAAS,KAAK,aAAa,MAClC,CAAC,OAAO,OAAO,EACf;oCACA,SAAS,IAAI,CAAC,OAAO,SAAS;gCAChC,OAAO,IAAI,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,EAAE;oCACzD,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,OAAO;gCACjD;gCAEA,8CAA8C;gCAC9C,IAAI,OAAO,MAAM,KAAK,aAAa,IAAI;oCACrC,gBAAgB,IAAI,CAAC,OAAO,MAAM;gCACpC;gCAEA,wBAAwB;gCACxB,IAAI,eAAe,OAAO,MAAM,KAAK,YAAY,EAAE,EAAE;oCACnD,mBAAmB,OAAO,IAAI;gCAChC;4BACF;4BAEA,wBAAwB;4BACxB,IAAI,UAAU,MAAM,GAAG,GAAG;gCACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,eAAe,CAAC;gCAC/D,MAAM,aAAa,MAAM,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;gCAC1C,IAAI,WAAW,OAAO,IAAI,WAAW,KAAK,EAAE;oCAC1C,WAAW,KAAK,CAAC,OAAO;kFAAC,CAAC;4CACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG;wCAG9B;;gCACF;4BACF;4BAEA,yBAAyB;4BACzB,IAAI,SAAS,MAAM,GAAG,GAAG;gCACvB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,cAAc,CAAC;gCAC7D,MAAM,cAAc,MAAM,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;gCAC3C,IAAI,YAAY,OAAO,IAAI,YAAY,KAAK,EAAE;oCAC5C,0BAA0B;oCAC1B,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;wCAClC,IAAI,OAAO,SAAS,EAAE;4CACpB,MAAM,QAAQ,YAAY,KAAK,CAAC,IAAI;gGAClC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,SAAS;;4CAElC,IAAI,OAAO;gDACT,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;4CACnC;wCACF;oCACF;gCACF;4BACF;4BAEA,gCAAgC;4BAChC,IAAI,gBAAgB,MAAM,GAAG,GAAG;gCAC9B,QAAQ,GAAG,CACT,CAAC,eAAe,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;gCAE1D,MAAM,cACJ,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;gCACzC,MAAM,qBAAqB,MAAM,CAAA,GAAA,yJAAA,CAAA,wBAAqB,AAAD,EACnD,iBACA;gCAGF,IACE,mBAAmB,OAAO,IAC1B,mBAAmB,aAAa,EAChC;oCACA,wBAAwB;oCACxB,OAAO,OAAO,CAAC,mBAAmB,aAAa,EAAE,OAAO;kFACtD,CAAC,CAAC,QAAQ,KAAK;4CACb,gCAAgC;4CAChC,MAAM,SAAS,KAAK,MAAM,IAAI;4CAE9B,kCAAkC;4CAClC,IAAI,WAAW,cAAc,WAAW,UAAU;gDAChD,gBAAgB,CAAC,OAAO,GAAG;4CAC7B,OAAO,IAAI,WAAW,gBAAgB;gDACpC,gBAAgB,CAAC,OAAO,GAAG;4CAC7B,OAAO,IAAI,WAAW,oBAAoB;gDACxC,gBAAgB,CAAC,OAAO,GAAG;4CAC7B,OAAO;gDACL,gBAAgB,CAAC,OAAO,GAAG;4CAC7B;4CAEA,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EACzC,gBAAgB,CAAC,OAAO;wCAE5B;;gCAEJ;4BACF;4BAEA,+DAA+D;4BAC/D,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;gCAClC,IACE,OAAO,MAAM,KAAK,aAAa,MAC/B,CAAC,gBAAgB,CAAC,OAAO,MAAM,CAAC,EAChC;oCACA,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG;gCACpC;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kCAAkC;wBAClD;wBAEA,8CAA8C;wBAC9C,iBAAiB;wBACjB,gBAAgB;wBAChB,iBAAiB;oBACnB;;gBAEA;YACF;QACF;8BAAG;QAAC,OAAO;QAAI,OAAO;QAAS;KAAY,GAAG,wCAAwC;IAEtF,6EAA6E;IAC7E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO,SAAS;gBAClB,yDAAyD;gBACzD,MAAM,iBAAiB,MAAM,OAAO,CAAC,MAAM;0DACzC,CAAC,SAAW,CAAC,aAAa,CAAC,OAAO,MAAM,CAAC,EAAE,UAAU;;gBAGvD,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC7B,QAAQ,GAAG,CACT,CAAC,qBAAqB,EAAE,eAAe,MAAM,CAAC,yCAAyC,CAAC;oBAE1F,sDAAsD;oBACtD,MAAM,QAAQ;qDAAW;wBACvB,6CAA6C;wBAC/C;oDAAG;oBACH;+CAAO,IAAM,aAAa;;gBAC5B;YACF;QACF;8BAAG;QAAC,OAAO;QAAS;KAAc;IAElC,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO,IAAI;gBACb,kBAAkB;gBAElB,gCAAgC;gBAChC,MAAM;oEAA2B;wBAC/B,IAAI;4BACF,MAAM,qBAIC,EAAE;4BACT,MAAM,gBAIC,EAAE;4BACT,MAAM,iBAIA,EAAE;4BAER,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW;gCAC5B,QAAQ,IAAI,CAAC,yCAAyC;gCACtD;4BACF;4BAEA,SAAS,OAAO;gFAAC,CAAC;oCAChB,IAAI;wCACF,wBAAwB;wCACxB,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;4CAC3C,QAAQ,IAAI,CAAC,uCAAuC;4CACpD;wCACF;wCAEA,yBAAyB;wCACzB,IAAI,QAAQ,QAAQ,EAAE;wCAEtB,gCAAgC;wCAChC,IAAI,CAAC,QAAQ,OAAO,EAAE;4CACpB,QAAQ,IAAI,CAAC,wCAAwC;4CACrD;wCACF;wCAEA,gBAAgB;wCAChB,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK;wCACnC,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GAAG;4CAC5C,MAAM,OAAO;gGAAC,CAAC;oDACb,IAAI,CAAC,WAAW,UAAU,WAAW;wDACnC,QAAQ,IAAI,CACV,yDACA;wDAEF;oDACF;oDAEA,MAAM,YAAY,UAAU,QAAQ,CAAC,SAAS,CAAC,WAAW;oDAC1D,MAAM,gBAAgB;wDACpB,GAAG,SAAS;wDACZ,WAAW,IAAI,KAAK,QAAQ,SAAS,IAAI,KAAK,GAAG;wDACjD,QAAQ,QAAQ,MAAM;wDACtB,UAAU,QAAQ,QAAQ;oDAC5B;oDAEA,IACE;wDACE;wDACA;wDACA;wDACA;wDACA;wDACA;wDACA;wDACA;qDACD,CAAC,QAAQ,CAAC,YACX;wDACA,mBAAmB,IAAI,CAAC;oDAC1B,OAAO;wDACL,cAAc,IAAI,CAAC;oDACrB;gDACF;;wCACF;wCAEA,wBAAwB;wCACxB,MAAM,OAAO,QAAQ,OAAO,CAAC,IAAI;wCACjC,IAAI,OAAO,SAAS,YAAY,KAAK,MAAM,GAAG,GAAG;4CAC/C,MAAM,WAAW;4CACjB,MAAM,UAAU,KAAK,KAAK,CAAC;4CAC3B,IAAI,SAAS;gDACX,QAAQ,OAAO;oGAAC,CAAC;wDACf,IAAI;4DACF,yBAAyB;4DACzB,MAAM,SAAS,IACZ,OAAO,CAAC,gBAAgB,IACxB,KAAK,CAAC,IAAI,CAAC,EAAE;4DAChB,iDAAiD;4DACjD,MAAM,QAAQ,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;4DACnC,eAAe,IAAI,CAAC;gEAClB;gEACA;gEACA,WAAW,IAAI,KAAK,QAAQ,SAAS,IAAI,KAAK,GAAG;4DACnD;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,IAAI,CACV,qCACA,KACA;wDAEJ;oDACF;;4CACF;wCACF;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,IAAI,CACV,yCACA,SACA;oCAEJ;gCACF;;4BAEA,mCAAmC;4BACnC,mBAAmB,IAAI;gFACrB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;4BAEvD,cAAc,IAAI;gFAChB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;4BAEvD,eAAe,IAAI;gFACjB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;4BAGvD,cAAc,mBAAmB,KAAK,CAAC,GAAG,MAAM,oBAAoB;4BACpE,aAAa,cAAc,KAAK,CAAC,GAAG,MAAM,oBAAoB;4BAC9D,SAAS,eAAe,KAAK,CAAC,GAAG,MAAM,oBAAoB;wBAC7D,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CACX,kDACA;wBAEJ,SAAU;4BACR,kBAAkB;wBACpB;oBACF;;gBAEA;YACF;QACF;8BAAG;QAAC,OAAO;QAAI;KAAS;IAExB,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,gBAAgB,CAAC,OAAO;gBAC3B,QAAQ,GAAG,CAAC;gBACZ,2CAA2C;gBAC3C,IAAI,SAAS;oBACX;gBACF;YACF;QACF;8BAAG;QAAC;QAAc;QAAO;KAAQ;IAEjC,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,MAAM,oBAAoB,OAAO;QAC/B,mEAAmE;QACnE,IAAI,aAAa,CAAC,SAAS,EAAE;YAC3B,kBAAkB,aAAa,CAAC,SAAS;YACzC,qBAAqB;YACrB;QACF;QAEA,4EAA4E;QAC5E,MAAM,iBAAiB,OAAO,SAAS,KACrC,CAAC,IAAM,EAAE,MAAM,KAAK,YAAY,EAAE,IAAI,EAAE;QAE1C,IAAI,gBAAgB,MAAM;YACxB,kBAAkB,eAAe,IAAI;YACrC,qBAAqB;YACrB;QACF;QAEA,IAAI;YACF,6CAA6C;YAC7C,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE;YACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,kBAAkB,OAAO,IAAI;gBAC7B,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,6DAA6D;YAC7D,QAAQ,GAAG,CACT;QAEJ;IACF;IAEA,IAAI,kBAAkB;QACpB,qBACE,6LAAC,iJAAA,CAAA,UAAgB;YACf,YAAY;YACZ,WAAW;YACX,OAAO;YACP,YAAY;YACZ,SAAS,IAAM,oBAAoB;;;;;;IAGzC;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,MAAM,aAAa,aAAa,CAAC,OAAO;QACxC,IAAI,YAAY;YACd,kBAAkB;YAClB,yBAAyB;YACzB,qBAAqB;YACrB,wBAAwB,OAAO,8BAA8B;QAC/D;IACF;IAEA,IAAI,iBAAiB;QACnB,qBACE,6LAAC;YACC,WAAW,CAAC,8BAA8B,EAAE,CAAC,YAAY,aAAa,IAAI;;8BAE1E,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,mBAAmB;0CAElC,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAIlC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAS;4BACP,mBAAmB;4BACnB,uBAAuB;wBACzB;;0CAEA,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;gCAAU;gCAAuB;gCAAY;;;;;;;sCAC7D,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,OAAM;sCAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIzB,6LAAC;oBAAI,WAAU;8BAEZ,mBAAmB,OAAO,cAEvB,kBAAkB,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBACvC,kDAAkD;wBAClD,MAAM,cACJ,OAAO,QAAQ,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI;wBAExD,MAAM,WACJ,eACA,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,GAC/C,YAAY,KAAK,CAAC,GAAG,GAAG,WAAW,KACnC;wBAEN,qBACE,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,kBAAkB,OAAO,EAAE;;sDAE1C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDACV,KAAK,OAAO,iBAAiB,IAAI;oDACjC,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAe;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,WACb,gBACA,OAAO,IAAI,KAAK,cACd,aACA;;;;;;;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;wCAEZ,OAAO,EAAE,KAAK,aAAa,MAC1B,aAAa,CAAC,OAAO,EAAE,CAAC,KAAK,gCAC3B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,QAAQ;4CACR,OAAM;sDAEN,cAAA,6LAAC,qMAAA,CAAA,OAAQ;gDAAC,WAAU;;;;;;;;;;;wCAKzB,OAAO,EAAE,KAAK,aAAa,oBAC1B,6LAAC,+IAAA,CAAA,eAAY;4CACX,MAAM,yBAAyB,OAAO,EAAE;4CACxC,cAAc,CAAC;gDACb,IAAI,MAAM;oDACR,wBAAwB,OAAO,EAAE;gDACnC,OAAO,IAAI,yBAAyB,OAAO,EAAE,EAAE;oDAC7C,wBAAwB;gDAC1B;4CACF;;8DAEA,6LAAC,+IAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,6LAAC,+IAAA,CAAA,sBAAmB;oDAClB,OAAM;oDACN,iBAAiB,IACf,wBAAwB;;wDAIzB,aAAa,CAAC,OAAO,EAAE,CAAC,KAAK,wBAC5B,6LAAC,+IAAA,CAAA,mBAAgB;4DACf,SAAS,IACP,wBAAwB,OAAO,EAAE;4DAEnC,UAAU,gBAAgB,CAAC,OAAO,EAAE,CAAC;sEAEpC,gBAAgB,CAAC,OAAO,EAAE,CAAC,iBAC1B;;kFACE,6LAAC;wEAAI,WAAU;;;;;;oEAA6F;;6FAI9G;;kFACE,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAA+B;;;;;;;;wDAQ1D,CAAC,oBAAoB,YACnB,oBAAoB,eACnB,OAAO,IAAI,KAAK,QAAS,mBAC3B;;gEACG,oBAAoB,YACnB,OAAO,IAAI,KAAK,0BACd,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,oBAAoB,OAAO,EAAE;;sFAG/B,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAIxC,oBAAoB,YACnB,OAAO,IAAI,KAAK,6BACd,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,mBAAmB,OAAO,EAAE;;sFAG9B,6LAAC,mNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAI3C,oBAAoB,YACnB,CAAC,OAAO,IAAI,KAAK,YACf,OAAO,IAAI,KAAK,WAAW,mBAC3B,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,sBAAsB,OAAO,EAAE;oEAEjC,WAAU;;sFAEV,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAIxC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8EACtB,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,iBAAiB,OAAO,EAAE;oEACzC,WAAU;;sFAEV,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnIzC,GAAG,OAAO,EAAE,EAAE;;;;;oBA8IzB,KAEA,MAAM,OAAO,EAAE,IAAI,CAAC;wBAClB,6EAA6E;wBAC7E,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;wBAE/C,kDAAkD;wBAClD,MAAM,cACJ,YAAY,UAAU,YACtB,OAAO,IAAI,EAAE,UAAU,YACvB,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI;wBAEzC,MAAM,WACJ,eACA,gBAAgB,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,GACnD,YAAY,KAAK,CAAC,GAAG,GAAG,WAAW,KACnC;wBAEN,qBACE,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,kBAAkB,OAAO,MAAM;;sDAE9C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDACV,KACE,YAAY,UAAU,qBACtB,OAAO,IAAI,EAAE,UAAU,qBACvB;oDAEF,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAe;;;;;;8DAC5B,6LAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,WACb,gBACA,OAAO,IAAI,KAAK,cACd,aACA;;;;;;gDAGP,OAAO,MAAM,KAAK,aAAa,oBAC9B,6LAAC;oDAAE,WAAU;8DACV,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,GAC3C,CAAC,SAAS,EAAE,AAAC,OAAO,OAAO,CAAqC,QAAQ,EAAE,GAC1E,YAAY,CAAC,OAAO,MAAM,CAAC,EAAE,UAAU,WACrC,CAAC,SAAS,EAAE,YAAY,CAAC,OAAO,MAAM,CAAC,EAAE,UAAU,UAAU,GAC7D;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;wCAEZ,OAAO,MAAM,KAAK,aAAa,MAC9B,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,gCAC/B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,QAAQ;4CACR,OAAM;sDAEN,cAAA,6LAAC,qMAAA,CAAA,OAAQ;gDAAC,WAAU;;;;;;;;;;;wCAKzB,OAAO,MAAM,KAAK,aAAa,oBAC9B,6LAAC,+IAAA,CAAA,eAAY;4CACX,MAAM,yBAAyB,OAAO,MAAM;4CAC5C,cAAc,CAAC;gDACb,IAAI,MAAM;oDACR,wBAAwB,OAAO,MAAM;gDACvC,OAAO,IAAI,yBAAyB,OAAO,MAAM,EAAE;oDACjD,wBAAwB;gDAC1B;4CACF;;8DAEA,6LAAC,+IAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,6LAAC,+IAAA,CAAA,sBAAmB;oDAClB,OAAM;oDACN,iBAAiB,IACf,wBAAwB;;wDAIzB,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,wBAChC,6LAAC,+IAAA,CAAA,mBAAgB;4DACf,SAAS,IACP,wBAAwB,OAAO,MAAM;4DAEvC,UAAU,gBAAgB,CAAC,OAAO,MAAM,CAAC;sEAExC,gBAAgB,CAAC,OAAO,MAAM,CAAC,iBAC9B;;kFACE,6LAAC;wEAAI,WAAU;;;;;;oEAA6F;;6FAI9G;;kFACE,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAA+B;;;;;;;;wDAQ1D,CAAC,oBAAoB,YACnB,oBAAoB,eACnB,OAAO,IAAI,KAAK,QAAS,mBAC3B;;gEACG,oBAAoB,YACnB,OAAO,IAAI,KAAK,0BACd,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,oBAAoB,OAAO,MAAM;;sFAGnC,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAIxC,oBAAoB,YACnB,OAAO,IAAI,KAAK,6BACd,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,mBAAmB,OAAO,MAAM;;sFAGlC,6LAAC,mNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAI3C,oBAAoB,YACnB,CAAC,OAAO,IAAI,KAAK,YACf,OAAO,IAAI,KAAK,WAAW,mBAC3B,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,sBAAsB,OAAO,MAAM;oEAErC,WAAU;;sFAEV,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAIxC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8EACtB,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,iBAAiB,OAAO,MAAM;oEAEhC,WAAU;;sFAEV,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnJzC,GAAG,OAAO,MAAM,EAAE;;;;;oBA8J7B;;;;;;gBAGL,qBAAqB,gCACpB,6LAAC,iJAAA,CAAA,UAAa;oBACZ,MAAM;oBACN,QAAQ;oBACR,cAAc,CAAC;wBACb,qBAAqB;wBACrB,IAAI,CAAC,MAAM;4BACT,kBAAkB;4BAClB,yBAAyB;wBAC3B;oBACF;oBACA,cAAc,eAAe,EAAE,KAAK,aAAa;oBACjD,8BAA8B;;;;;;8BAKlC,6LAAC,8IAAA,CAAA,cAAW;oBACV,MAAM;oBACN,cAAc;8BAEd,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;0CACjB,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,6LAAC,8IAAA,CAAA,yBAAsB;kDAAC;;;;;;;;;;;;0CAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,oBAAiB;wCAAC,UAAU;kDAAc;;;;;;kDAC3C,6LAAC,8IAAA,CAAA,oBAAiB;wCAChB,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,6LAAC;oDAAI,WAAU;;;;;;gDAA0F;;2DAI3G;;;;;;;;;;;;;;;;;;;;;;;8BAQV,6LAAC,8IAAA,CAAA,cAAW;oBAAC,MAAM;oBAAkB,cAAc;8BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;0CACjB,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,6LAAC,8IAAA,CAAA,yBAAsB;kDAAC;;;;;;;;;;;;0CAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,oBAAiB;wCAAC,UAAU;kDAAc;;;;;;kDAC3C,6LAAC,8IAAA,CAAA,oBAAiB;wCAChB,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,6LAAC;oDAAI,WAAU;;;;;;gDAA0F;;2DAI3G;;;;;;;;;;;;;;;;;;;;;;;8BAQV,6LAAC,8IAAA,CAAA,cAAW;oBAAC,MAAM;oBAAgB,cAAc;8BAC/C,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;0CACjB,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,6LAAC,8IAAA,CAAA,yBAAsB;kDAAC;;;;;;;;;;;;0CAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,oBAAiB;wCAAC,UAAU;kDAAc;;;;;;kDAC3C,6LAAC,8IAAA,CAAA,oBAAiB;wCAChB,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,6LAAC;oDAAI,WAAU;;;;;;gDAA0F;;2DAI3G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQhB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,8BAA8B,EAAE,CAAC,YAAY,aAAa,IAAI;;YAGzE,uBACC,6LAAC,wJAAA,CAAA,UAAsB;gBACrB,SAAS,MAAM,EAAE;gBACjB,gBAAgB;;;;;;0BAGpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAW,GAAG,YAAY,kCAAkC,gBAAgB;gCAC5E,SAAS;0CAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC,6IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,mBAAmB;;sDAElC,6LAAC,qIAAA,CAAA,cAAW;4CACV,KAAK,MAAM,SAAS,IAAI;4CACxB,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,MAAM,IAAI,EAAE,MAAM,GAAG,GAAG,iBAAiB;;;;;;;;;;;;8CAG9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyB,MAAM,IAAI;;;;;;wCAChD,oBAAoB,0BACnB,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAErC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAMxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,uBAAuB;oDACzB;8DAEA,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;;wDAA6B;wDACzC;wDAAY;;;;;;;;;;;;;sDAGlB,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CACjB,cAAA,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB;;0DAElC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;;oDAAW;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CACjB,cAAA,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB;;0DAEpC,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CAChB,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,WAAW,MAAM,GAAG,kBACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAClC,6LAAC;wDAEC,WAAU;wDACV,SAAS;4DACP,sBAAsB;4DACtB,mBAAmB;wDACrB;wDACA,OAAO,MAAM,QAAQ,IAAI;kEAExB,MAAM,QAAQ,EAAE,WAAW,MAAM,oBAClC,MAAM,IAAI,KAAK,wBACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,KAAK,MAAM,GAAG;oEACd,KAAK;oEACL,SAAQ;;;;;;8EAEV,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;iFAIrB,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;4DAAC;;;;;;uDAxB7C;;;;;;;;;;0DA8BX,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAML,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAS7C,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CAChB,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,UAAU,MAAM,GAAG,kBACrB,6LAAC;wCAAI,WAAU;;4CACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC,IAAI,GAAG,EAAE;oDACpC,OAAO,IAAI,QAAQ;;sEAEnB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFACV,IAAI,QAAQ;;;;;;kFAEf,6LAAC;wEAAE,WAAU;kFACV,IAAI,QAAQ,EAAE,iBACb,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,GAAG,CAAC;;;;;;;;;;;;;;;;;sEAI5D,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;sEAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;mDAxBrB;;;;;4CA4BR,UAAU,MAAM,GAAG,mBAClB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAOP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAO7C,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CAChB,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,MAAM,MAAM,GAAG,kBACjB,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM;gDAC5B,0BAA0B;gDAC1B,MAAM,SAAS,KAAK,GAAG,CACpB,OAAO,CAAC,gBAAgB,IACxB,KAAK,CAAC,IAAI,CAAC,EAAE;gDAChB,uBAAuB;gDACvB,MAAM,OAAO,IAAI,KAAK,KAAK,SAAS;gDACpC,MAAM,gBAAgB,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;gDAE1H,qBACE,6LAAC;oDAEC,WAAU;oDACV,SAAS,IACP,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE,UAAU;oDAElC,OAAO,KAAK,KAAK;;sEAEjB,6LAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE;;;;;;sEAEf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EACV,QACA,KAAK,KAAK,CAAC,MAAM,GAAG,KAChB,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,QAC9B,KAAK,KAAK;;;;;;sFAGlB,6LAAC;4EAAE,WAAU;sFACV;;;;;;;;;;;;8EAGL,6LAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;;;mDAzBA;;;;;4CA8BX;4CACC,MAAM,MAAM,GAAG,mBACd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAOP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAO7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;;sDAE1B,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;gCAIP,oBAAoB,YACnB,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,MAAM,GAAG,mBACrB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,gCAAgC;;sDAE/C,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;gCAKX,oBAAoB,0BACnB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,oBAAoB;;sDAEnC,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;gCAKT,CAAC,CAAC,oBAAoB,YAAY,MAAM,OAAO,EAAE,WAAW,CAAC,mBAC5D,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;wCACP,+DAA+D;wCAC/D,IAAI,oBAAoB,UAAU;4CAChC,gCAAgC;wCAClC,OAAO;4CACL,mBAAmB;wCACrB;oCACF;;sDAEA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOf,qBAAqB,gCACpB,6LAAC,iJAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,cAAc,eAAe,EAAE,KAAK,aAAa;;;;;;0BAKrD,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAiB,cAAc;0BAChD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,6LAAC,8IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAM1B,6LAAC;4BAAI,WAAU;sCACZ,MAAM,OAAO,EACV,OAAO,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,IAAI,6BAA6B;6BACpF,IAAI,CAAC;gCACJ,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;gCAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;gCAEJ,qBACE,6LAAC;oCAEC,WAAU;oCACV,SAAS,IAAM,sBAAsB,OAAO,MAAM;;sDAElD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDACV,KACE,YAAY,UAAU,qBAAqB;oDAE7C,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,YAAY,UAAU,YAAY;;;;;;8DAErC,6LAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,cACb,aACA;;;;;;;;;;;;;mCAtBH,CAAC,SAAS,EAAE,OAAO,MAAM,EAAE;;;;;4BA2BtC;;;;;;sCAEJ,6LAAC,8IAAA,CAAA,oBAAiB;sCAChB,cAAA,6LAAC,8IAAA,CAAA,oBAAiB;gCAAC,UAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;0BAMjD,6LAAC,8IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,6LAAC,8IAAA,CAAA,yBAAsB;8CACpB,eAAe,aAAa,CAAC,YAAY,iBACxC;;4CAAE;4CACmD;0DACnD,6LAAC;0DACE,aAAa,CAAC,YAAY,EAAE,UAAU,YACrC;;;;;;4CACK;0DAET,6LAAC;;;;;4CAAK;;uDAKR;;;;;;;;;;;;sCAIN,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,UAAU;oCACV,SAAS;wCACP,6BAA6B;wCAC7B,eAAe;oCACjB;8CACD;;;;;;8CAGD,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;YAMT,uBAAuB,uBACtB,6LAAC,iJAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,cAAc;gBACd,SAAS,MAAM,EAAE;;;;;;YAKpB,mBAAmB,uBAClB,6LAAC,6IAAA,CAAA,UAAW;gBACV,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,YAAY;;;;;;YAKf,uBACC,6LAAC,qJAAA,CAAA,UAAmB;gBAClB,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,QAAQ,IAAM,sBAAsB;gBACpC,WAAW,CAAC;oBACV,gCAAgC;oBAChC,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;oBACvC,IAAI,UAAU,aAAa,EAAE,OAAO,aAAa,EAAE,EAAE;wBACnD,UAAU,gBAAgB,CAAC;oBAC7B;oBAEA,4EAA4E;oBAC5E,WAAW;wBACT,OAAO,QAAQ,CAAC,MAAM;oBACxB,GAAG;gBACL;;;;;;YAKH,uBACC,6LAAC,0IAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,SAAS,MAAM,EAAE;gBACjB,WAAW,MAAM,IAAI;;;;;;YAKxB,mBAAmB,WAAW,MAAM,GAAG,mBACtC,6LAAC,6IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,OAAO,WAAW,GAAG,CAAC,CAAC,QAAU,CAAC;wBAChC,GAAG,KAAK;wBACR,0CAA0C;wBAC1C,MACE,MAAM,QAAQ,EAAE,WAAW,MAAM,oBACjC,MAAM,IAAI,KAAK,UACX,UACA;oBACR,CAAC;gBACD,cAAc;gBACd,UAAU,MAAM,IAAI,IAAI;;;;;;0BAK5B,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAgB,cAAc;0BAC/C,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASd,8CAA8C;IAC9C,eAAe,oBAAoB,QAAgB;QACjD,oBAAoB;QACpB,qBAAqB;QACrB,wBAAwB,OAAO,8BAA8B;IAC/D;IAEA,qCAAqC;IACrC,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB;QACrC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,kBACA,uHAAA,CAAA,YAAS,CAAC,SAAS;YAErB,IAAI,OAAO,OAAO,EAAE;gBAClB,uCAAuC;gBACvC,qBAAqB;gBAErB,mDAAmD;gBACnD,IAAI,MAAM,OAAO,EAAE;oBACjB,MAAM,iBAAiB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;wBACxC,IAAI,OAAO,MAAM,KAAK,kBAAkB;4BACtC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,uHAAA,CAAA,YAAS,CAAC,SAAS;4BAAC;wBAChD;wBACA,OAAO;oBACT;oBACA,MAAM,OAAO,GAAG;gBAClB;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,gEAAgE;gBAChE,WAAW;oBACT,kBAAkB;gBACpB,GAAG;gBAEH,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,oDAAoD;IACpD,eAAe,mBAAmB,QAAgB;QAChD,oBAAoB;QACpB,oBAAoB;QACpB,wBAAwB,OAAO,8BAA8B;IAC/D;IAEA,kCAAkC;IAClC,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB;QACrC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,kBACA,uHAAA,CAAA,YAAS,CAAC,MAAM;YAElB,IAAI,OAAO,OAAO,EAAE;gBAClB,uCAAuC;gBACvC,oBAAoB;gBAEpB,mDAAmD;gBACnD,IAAI,MAAM,OAAO,EAAE;oBACjB,MAAM,iBAAiB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;wBACxC,IAAI,OAAO,MAAM,KAAK,kBAAkB;4BACtC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM;4BAAC;wBAC7C;wBACA,OAAO;oBACT;oBACA,MAAM,OAAO,GAAG;gBAClB;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,gEAAgE;gBAChE,WAAW;oBACT,kBAAkB;gBACpB,GAAG;gBAEH,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qCAAqC;IACrC,eAAe,iBAAiB,QAAgB;QAC9C,oBAAoB;QACpB,kBAAkB;QAClB,wBAAwB,OAAO,8BAA8B;IAC7D,yDAAyD;IACzD,kEAAkE;IACpE;IAEA,+BAA+B;IAC/B,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB;QACrC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,OAAO,OAAO,EAAE;gBAClB,uCAAuC;gBACvC,kBAAkB;gBAElB,gEAAgE;gBAChE,IAAI,MAAM,OAAO,EAAE;oBACjB,MAAM,iBAAiB,MAAM,OAAO,CAAC,MAAM,CACzC,CAAC,SAAW,OAAO,MAAM,KAAK;oBAEhC,MAAM,OAAO,GAAG;gBAClB;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBAAqB;IACrB,eAAe;QACb,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE;YACzC,IAAI,OAAO,OAAO,EAAE;gBAClB,4CAA4C;gBAC5C,oBAAoB;gBAEpB,yBAAyB;gBACzB,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;gBAEvC,yBAAyB;gBACzB,UAAU,cAAc,CAAC,SAAS,MAAM,EAAE;gBAE1C,yCAAyC;gBACzC,UAAU,gBAAgB,CAAC;gBAE3B,0CAA0C;gBAC1C,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;gBACzD,mBAAmB,kBAAkB,CAAC,MAAM,EAAE;gBAE9C,6BAA6B;gBAC7B;gBAEA,2BAA2B;gBAC3B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,4DAA4D;IAC5D,SAAS,sBAAsB,QAAgB;QAC7C,eAAe;QACf,6BAA6B;IAC/B;IAEA,qCAAqC;IACrC,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,aAAa;QAChC,gBAAgB;QAChB,IAAI;YACF,oDAAoD;YACpD,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,aACA,uHAAA,CAAA,YAAS,CAAC,MAAM;YAGlB,IAAI,OAAO,OAAO,EAAE;gBAClB,kBAAkB;gBAClB,6BAA6B;gBAC7B,gCAAgC;gBAEhC,mDAAmD;gBACnD,IAAI,MAAM,OAAO,IAAI,aAAa;oBAChC,MAAM,iBAAiB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;wBACxC,IAAI,OAAO,MAAM,KAAK,aAAa;4BACjC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM;4BAAC;wBAC7C;wBACA,IAAI,OAAO,MAAM,KAAK,YAAY,EAAE,EAAE;4BACpC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM;4BAAC;wBAC7C;wBACA,OAAO;oBACT;oBACA,MAAM,OAAO,GAAG;oBAEhB,2CAA2C;oBAC3C,mBAAmB,uHAAA,CAAA,YAAS,CAAC,MAAM;gBACrC;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,gEAAgE;gBAChE,WAAW;oBACT,kBAAkB;gBACpB,GAAG;gBAEH,2BAA2B;gBAC3B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,oBAAoB;gBACpB,eAAe;YACjB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBAAqB;IACrB,eAAe;QACb,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,EAAE;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,uBAAuB;gBACvB,mBAAmB;gBAEnB,yBAAyB;gBACzB,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;gBAEvC,yBAAyB;gBACzB,UAAU,cAAc,CAAC,SAAS,MAAM,EAAE;gBAE1C,yCAAyC;gBACzC,UAAU,gBAAgB,CAAC;gBAE3B,0CAA0C;gBAC1C,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;gBACzD,mBAAmB,kBAAkB,CAAC,MAAM,EAAE;gBAE9C,6BAA6B;gBAC7B;gBAEA,2BAA2B;gBAC3B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;AACF;GAtyEwB;;QAMA,6HAAA,CAAA,eAAY;QA2GjB,6HAAA,CAAA,eAAY;QACT,6HAAA,CAAA,eAAY;QA6NV,sIAAA,CAAA,wBAAqB;;;KA/UrB", "debugId": null}}]}